<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
  <title>Stacked Cards</title>
  <style>
    :root { --radius: 24px; --shadow: 0 18px 36px rgba(0,0,0,.24); --text-shadow: 0 6px 18px rgba(0,0,0,.45); --phone-w: min(420px, 94vw); }
    * { box-sizing: border-box; }
    body { margin: 0; min-height: 100svh; display: grid; place-items: center; background: radial-gradient(1200px 600px at 20% -10%, #eef3ff 0%, transparent 60%), #0f1115; color:#1a1f2b; font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; }

    /* Phone frame */
    .phone { width: var(--phone-w); height: min(880px, calc(94svh - 20px)); border-radius: 36px; background: #f7f8fb; border: 1px solid #e9edf5; box-shadow: 0 26px 72px rgba(0,0,0,.42), 0 10px 24px rgba(0,0,0,.22); overflow: hidden; display: flex; flex-direction: column; }

    /* App bar */
    .appbar { background: #f3f5fa; border-bottom: 1px solid #e9edf5; padding: 14px 18px 12px; border-top-left-radius: 36px; border-top-right-radius: 36px; }
    .titlebar { display: flex; align-items: baseline; justify-content: space-between; gap: 12px; }
    .eyebrow { margin: 0 0 2px; color: #6a7590; font-size: clamp(11px, 2.4vw, 12px); letter-spacing: .12em; text-transform: uppercase; font-weight: 700; }
    .headline { font-weight: 800; font-size: clamp(20px, 6vw, 26px); line-height: 1.15; margin: 0; color:#283249; }
    .progress-meta { font-weight: 700; color: #5b6b87; font-size: clamp(12px, 3.2vw, 13px); white-space: nowrap; }

    /* Progress tracer (segmented) */
    .progress-steps { display: flex; gap: 6px; margin-top: 10px; }
    .progress-steps .step { position: relative; flex: 1; height: 5px; background: #e6ebf5; border-radius: 999px; overflow: hidden; box-shadow: inset 0 1px 0 rgba(255,255,255,.6); }
    .progress-steps .step::before { content: ""; position: absolute; inset: 0; width: var(--fill, 0%); background: linear-gradient(90deg, #6ea8ff, #5877ff); border-radius: inherit; }
    .progress-steps .step.is-complete { background: linear-gradient(90deg, #6ea8ff, #5877ff); }

    .content { position: relative; padding: 12px 12px 16px; flex: 1; overflow: auto; backdrop-filter: none; }

    /* Card stack sized for mobile */
    .stack { position: relative; width: calc(100% - 8px); height: clamp(420px, 58svh, 560px); margin-left: 6px; }
    .card { position: absolute; inset: 0; border-radius: var(--radius); overflow: hidden; box-shadow: var(--shadow); }
    .card img { width:100%; height:100%; object-fit: cover; transform: scale(1.04); display:block; }
    .card::after { content:""; position:absolute; inset:0; background:linear-gradient(0deg, rgba(0,0,0,.55), rgba(0,0,0,0) 65%); }
    .text { position:absolute; left:18px; right:18px; bottom:18px; color:#fff; text-shadow: var(--text-shadow); }
    .title { margin:0 0 4px; font-weight:800; font-size: clamp(28px, 8vw, 36px); line-height:1.05; letter-spacing:-.3px; }
    .subtitle { margin:0; font-weight:700; font-size: clamp(13px, 4vw, 16px); opacity:.96; }
    .is-front { z-index:3; transform: translateX(0) translateY(0) scale(1); }
    .is-mid   { z-index:2; transform: translateX(26px) translateY(12px) scale(.93); filter:saturate(.92) brightness(.97); }
    .is-back  { z-index:1; transform: translateX(56px) translateY(22px) scale(.88); filter:saturate(.88) brightness(.95); }

    /* Bottom action bar */
    .actionbar { display: flex; align-items: center; justify-content: space-between; gap: 12px; padding: 10px 14px calc(10px + env(safe-area-inset-bottom)); background: linear-gradient(180deg, rgba(247,248,251,0), rgba(247,248,251,.9) 18%), #f7f8fb; border-top: 1px solid #e9edf5; border-bottom-left-radius: 36px; border-bottom-right-radius: 36px; }
    .btn { width: 44px; height: 44px; border-radius: 999px; display: inline-flex; align-items: center; justify-content: center; background: #ffffff; border: 1px solid #e3e8f3; box-shadow: 0 6px 16px rgba(17,25,40,.08); color: #23324a; cursor: pointer; }
    .btn--primary { width: 52px; height: 52px; border: 0; background: linear-gradient(180deg, #5b8cff, #4f6bff); color: #fff; box-shadow: 0 14px 28px rgba(79,107,255,.35), 0 6px 14px rgba(79,107,255,.25); }
    .btn--ghost { background: #fff; }
    .btn svg { width: 24px; height: 24px; }
    .sliderctrl { display: flex; align-items: center; gap: 10px; flex: 1; justify-content: center; }
    .slider-track { display: flex; align-items: center; gap: 6px; }
    .dot { width: 7px; height: 7px; border-radius: 50%; background: #cfd7ea; }
    .dot.is-done { background: #7e95ff; }
    .dot.is-active { background: #4f6bff; box-shadow: 0 0 0 4px rgba(79,107,255,.18); }
  </style>
  <!-- Use local images if your environment blocks external URLs -->
</head>
<body>
  <div class="phone">
    <div class="appbar">
      <div class="titlebar">
        <div>
          <p class="eyebrow">Featured module</p>
          <p class="headline">Explore the natural wonders of the world</p>
        </div>
        <div class="progress-meta">3 / 8</div>
      </div>
      <div class="progress-steps">
        <span class="step is-complete"></span>
        <span class="step is-complete"></span>
        <span class="step" style="--fill: 40%"></span>
        <span class="step"></span>
        <span class="step"></span>
        <span class="step"></span>
        <span class="step"></span>
        <span class="step"></span>
      </div>
    </div>
    <div class="content">
      <div class="stack">
        <article class="card is-front">
          <img alt="Jellyfish" src="https://images.unsplash.com/photo-1501612780327-45045538702b?q=80&w=1200&auto=format&fit=crop" />
          <div class="text">
            <h2 class="title">Jellyfish</h2>
            <p class="subtitle">Medusozoa</p>
          </div>
        </article>

        <article class="card is-mid" aria-hidden="true">
          <img alt="" src="https://images.unsplash.com/photo-1584311870297-91f3a1a996a7?q=80&w=1200&auto=format&fit=crop" />
        </article>

        <article class="card is-back" aria-hidden="true">
          <img alt="" src="https://images.unsplash.com/photo-1544551763-7ef420be2e37?q=80&w=1200&auto=format&fit=crop" />
        </article>
      </div>
    </div>
    <div class="actionbar">
      <button class="btn btn--ghost" aria-label="Favorite" title="Favorite">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
        </svg>
      </button>

      <div class="sliderctrl">
        <button class="btn" aria-label="Previous" title="Previous">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
            <path d="M15 18l-6-6 6-6"/>
          </svg>
        </button>
        <div class="slider-track" aria-hidden="true">
          <span class="dot is-done"></span>
          <span class="dot is-done"></span>
          <span class="dot is-active"></span>
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
        <button class="btn btn--primary" aria-label="Next" title="Next">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
            <path d="M9 18l6-6-6-6"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</body>
 </html>

