<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
  <title>Mobile Cards</title>
  <style>
    :root{
      --radius:24px; --shadow:0 16px 40px rgba(0,0,0,.28); --text-shadow:0 6px 18px rgba(0,0,0,.45);
    }
    *{box-sizing:border-box}
    body{margin:0;background:#0f1115;color:#1a1f2b;font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif}
    .screen{height:100dvh;display:flex;flex-direction:column;background:#f7f8fb;overflow:hidden}

    /* Header with title and segmented progress */
    .header{padding:16px 18px 10px;padding-top:calc(16px + env(safe-area-inset-top));background:#f3f5fa;border-bottom:1px solid #e9edf5}
    .toprow{display:flex;align-items:baseline;justify-content:space-between;gap:12px}
    .eyebrow{margin:0 0 2px;color:#6a7590;font-size:12px;letter-spacing:.12em;text-transform:uppercase;font-weight:700}
    .title{margin:0;color:#283249;font-weight:800;font-size:clamp(20px,6vw,26px);line-height:1.15}
    .progressMeta{font-weight:700;color:#5b6b87;font-size:13px;white-space:nowrap}
    .steps{display:flex;gap:6px;margin-top:10px}
    .steps .s{flex:1;height:5px;border-radius:999px;background:#e6ebf5;position:relative;overflow:hidden;box-shadow:inset 0 1px 0 rgba(255,255,255,.6)}
    .steps .s.done{background:linear-gradient(90deg,#6ea8ff,#5877ff)}
    .steps .s.active::before{content:"";position:absolute;inset:0;width:45%;background:linear-gradient(90deg,#6ea8ff,#5877ff)}

    /* Cards: horizontal scroll-snap so nothing is hidden */
    .scroller{scroll-snap-type:x mandatory;display:flex;gap:16px;overflow-x:auto;overflow-y:hidden;padding:18px 18px 18px;scroll-padding-inline:18px;flex:1;min-height:0}
    .scroller::-webkit-scrollbar{height:8px}
    .scroller::-webkit-scrollbar-thumb{background:#cfd7ea;border-radius:999px}
    .card{scroll-snap-align:center;flex:0 0 clamp(280px,86vw,340px);height:100%;min-height:320px;max-height:520px;border-radius:24px;overflow:hidden;box-shadow:var(--shadow);position:relative;background:#ccc}
    .card img{width:100%;height:100%;object-fit:cover;display:block}
    .card::after{content:"";position:absolute;inset:0;background:linear-gradient(0deg,rgba(0,0,0,.55),rgba(0,0,0,0) 65%)}
    .card .txt{position:absolute;left:16px;right:16px;bottom:16px;color:#fff;text-shadow:var(--text-shadow)}
    .card .name{margin:0 0 4px;font-weight:800;font-size:clamp(22px,6.5vw,28px)}
    .card .sub{margin:0;font-weight:700;opacity:.95}

    /* Bottom controls */
    .controls{margin-top:auto;display:flex;align-items:center;justify-content:space-between;gap:12px;padding:10px 14px calc(10px + env(safe-area-inset-bottom));background:#f7f8fb;border-top:1px solid #e9edf5}
    .btn{width:44px;height:44px;border-radius:999px;display:inline-flex;align-items:center;justify-content:center;background:#fff;border:1px solid #e3e8f3;box-shadow:0 6px 16px rgba(17,25,40,.08);color:#23324a}
    .btn--primary{width:52px;height:52px;border:0;background:linear-gradient(180deg,#5b8cff,#4f6bff);color:#fff;box-shadow:0 14px 28px rgba(79,107,255,.35),0 6px 14px rgba(79,107,255,.25)}
    .btn svg{width:22px;height:22px}
    .dots{display:flex;align-items:center;gap:6px}
    .dot{width:7px;height:7px;border-radius:50%;background:#cfd7ea}
    .dot.done{background:#7e95ff}
    .dot.active{background:#4f6bff;box-shadow:0 0 0 4px rgba(79,107,255,.18)}
  </style>
</head>
<body>
  <main class="screen">
    <header class="header">
      <div class="toprow">
        <div>
          <p class="eyebrow">Featured module</p>
          <h1 class="title">Explore the natural wonders of the world</h1>
        </div>
        <div class="progressMeta">3 / 8</div>
      </div>
      <div class="steps">
        <span class="s done"></span>
        <span class="s done"></span>
        <span class="s active"></span>
        <span class="s"></span>
        <span class="s"></span>
        <span class="s"></span>
        <span class="s"></span>
        <span class="s"></span>
      </div>
    </header>

    <section class="scroller" aria-label="Cards">
      <article class="card">
        <img src="https://images.unsplash.com/photo-1501612780327-45045538702b?q=80&w=1200&auto=format&fit=crop" alt="Jellyfish" />
        <div class="txt"><h2 class="name">Jellyfish</h2><p class="sub">Medusozoa</p></div>
      </article>
      <article class="card">
        <img src="https://images.unsplash.com/photo-1584311870297-91f3a1a996a7?q=80&w=1200&auto=format&fit=crop" alt="Coral" />
        <div class="txt"><h2 class="name">Coral Reef</h2><p class="sub">Cnidaria</p></div>
      </article>
      <article class="card">
        <img src="https://images.unsplash.com/photo-1544551763-7ef420be2e37?q=80&w=1200&auto=format&fit=crop" alt="Bird" />
        <div class="txt"><h2 class="name">Sea Bird</h2><p class="sub">Charadriiformes</p></div>
      </article>
    </section>

    <nav class="controls">
      <button class="btn" aria-label="Favorite" title="Favorite">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/></svg>
      </button>
      <div style="display:flex;align-items:center;gap:10px;flex:1;justify-content:center">
        <button class="btn" aria-label="Previous" title="Previous"><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><path d="M15 18l-6-6 6-6"/></svg></button>
        <div class="dots" aria-hidden="true">
          <span class="dot done"></span><span class="dot done"></span><span class="dot active"></span><span class="dot"></span><span class="dot"></span>
        </div>
        <button class="btn btn--primary" aria-label="Next" title="Next"><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true"><path d="M9 18l6-6-6-6"/></svg></button>
      </div>
    </nav>
  </main>
</body>
</html>


