<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <title>Swipe Experience • SuperDesign Canvas</title>
  <style>
    :root{
      --bg:#0a0a0a;--panel:#0f1318;--text:#e8ecf3;--muted:#9aa3ae;--accent:#ff6600;--hi:#ffd335;
      --top:0.45;--bottom:0.60;--segH:4px;--segGap:6px;--hazardH:6px;--radius:24px;
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{margin:0;background:linear-gradient(180deg,#0c1220 0,#0a0a0a 35%);font:14px/1.5 system-ui,-apple-system,Segoe UI,Roboto,Inter,sans-serif;color:var(--text);display:grid;grid-template-columns:340px 1fr;gap:24px;padding:24px}
    aside{background:var(--panel);border:1px solid rgba(255,255,255,.12);border-radius:14px;padding:16px}
    main{background:#0c1016;border:1px solid rgba(255,255,255,.12);border-radius:14px;padding:20px}
    h1{margin:0 0 6px;font-size:18px}
    h2{margin:18px 0 8px;font-size:13px;color:var(--muted)}
    .row{display:flex;align-items:center;gap:10px;margin:8px 0}
    .row label{width:120px;color:var(--muted)}
    input[type="range"]{width:100%}

    .phone{width:390px;height:844px;margin:0 auto;border-radius:28px;overflow:hidden;position:relative;background:#000;box-shadow:0 10px 40px rgba(0,0,0,.45)}
    .slide{position:absolute;inset:0;background:#000}
    .bg, .fg{position:absolute;inset:0;display:grid;place-items:center;will-change:transform}
    .bg img,.fg img{max-width:100%;max-height:100%;object-fit:contain}

    .overlay{position:absolute;inset:0;pointer-events:none;background:linear-gradient(to bottom,rgba(0,0,0,var(--top)) 0%,rgba(0,0,0,0) 55%,rgba(0,0,0,var(--bottom)) 100%)}
    .top{position:absolute;top:10px;left:10px;right:10px;display:grid;gap:6px}
    .hazard{height:var(--hazardH);display:grid;grid-auto-flow:column;gap:2px}
    .hazard span{border-radius:3px}
    .progressRow{display:grid;grid-template-columns:1fr auto;gap:8px;align-items:center}
    .segments{display:grid;grid-auto-flow:column;gap:var(--segGap)}
    .seg{height:var(--segH);background:rgba(255,255,255,.22);border-radius:3px;overflow:hidden}
    .seg>i{display:block;height:100%;background:var(--accent);width:0}
    .counter{justify-self:end;color:#fff;font-weight:600;font-size:12px}
    .chip{display:inline-grid;grid-auto-flow:column;gap:6px;align-items:center;padding:6px 10px;background:rgba(0,0,0,.35);border:1px solid rgba(255,255,255,.15);border-radius:12px;color:#fff;font-weight:600;width:max-content}

    .x{position:absolute;top:14px;left:12px;width:28px;height:28px;border-radius:8px;display:grid;place-items:center;color:#fff;background:rgba(0,0,0,.35);border:1px solid rgba(255,255,255,.2)}

    .zones{position:absolute;inset:0;display:grid;grid-template-columns:1fr 1fr}
    .zones>div{cursor:pointer}

    .glass{position:absolute;left:16px;right:16px;bottom:14px;padding:10px 12px;backdrop-filter:blur(16px);-webkit-backdrop-filter:blur(16px);background:rgba(0,0,0,.28);border:1px solid rgba(255,255,255,.15);border-radius:28px;display:flex;gap:10px;justify-content:center}
    .btn{width:44px;height:44px;border-radius:50%;display:grid;place-items:center;border:1px solid rgba(255,255,255,.15);background:rgba(0,0,0,.35);color:#fff}
  </style>
</head>
<body>
  <aside>
    <h1>Swipe Experience</h1>
    <p style="margin:0 0 10px;color:var(--muted)">Full-screen slide reader with header, parallax, and glass controls.</p>

    <h2>Slides</h2>
    <div class="row"><label>Count</label><input id="count" type="range" min="3" max="20" value="12"></div>
    <div class="row"><label>Current</label><input id="current" type="range" min="1" max="12" value="3"></div>

    <h2>Overlays</h2>
    <div class="row"><label>Top alpha</label><input id="top" type="range" min="0" max="90" value="45"></div>
    <div class="row"><label>Bottom alpha</label><input id="bottom" type="range" min="0" max="90" value="60"></div>
    <div class="row"><label>Accent</label><input id="accent" type="color" value="#ff6600"></div>

    <h2>Progress</h2>
    <div class="row"><label>Segments</label><input id="segments" type="range" min="3" max="20" value="12"></div>
    <div class="row"><label>Seg height</label><input id="segH" type="range" min="2" max="10" value="4"></div>
    <div class="row"><label>Seg gap</label><input id="segGap" type="range" min="2" max="10" value="6"></div>
    <div class="row"><label>Hazard height</label><input id="hazardH" type="range" min="2" max="14" value="6"></div>
    <div class="row"><label>Show Hazard</label><input id="showHazard" type="checkbox" checked></div>

    <h2>Actions</h2>
    <div class="row"><button id="prev">Prev</button><button id="next">Next</button></div>
  </aside>

  <main>
    <div class="phone" id="phone">
      <div class="slide">
        <div class="bg"><img id="bgImg" alt="bg" src="https://images.unsplash.com/photo-1505247964246-1f0a90443c36?q=80&w=1600&auto=format&fit=crop"></div>
        <div class="fg"><img id="fgImg" alt="fg" src="https://images.unsplash.com/photo-1581093458791-9d71b0d6f4bd?q=80&w=1600&auto=format&fit=crop"></div>
        <div class="overlay" id="overlay"></div>

        <div class="top">
          <div class="hazard" id="hazard"></div>
          <div class="progressRow">
            <div class="segments" id="segs"></div>
            <div class="counter" id="counter">3/12</div>
          </div>
          <div class="chip" id="chip">Quiz • Temporary Traffic Control</div>
        </div>

        <div class="x" title="Close">✕</div>

        <div class="zones">
          <div id="zoneL"></div>
          <div id="zoneR"></div>
        </div>

        <div class="glass">
          <div class="btn" title="Like">❤</div>
          <div class="btn" title="Save">🔖</div>
          <div class="btn" title="More">⋯</div>
        </div>
      </div>
    </div>
  </main>

  <script>
    const $=s=>document.querySelector(s);
    const state={count:12,current:3,segments:12,top:45,bottom:60,segH:4,segGap:6,hazardH:6,accent:'#ff6600',showHazard:true};
    const segs=$('#segs'),counter=$('#counter'),hazard=$('#hazard'),overlay=$('#overlay');
    const bgImg=$('#bgImg'),fgImg=$('#fgImg');

    function setVar(k,v){document.documentElement.style.setProperty(k,v)}

    function renderOverlay(){
      setVar('--top',(state.top/100).toFixed(2));
      setVar('--bottom',(state.bottom/100).toFixed(2));
      setVar('--segH',state.segH+'px');
      setVar('--segGap',state.segGap+'px');
      setVar('--hazardH',state.hazardH+'px');
      setVar('--accent',state.accent);
    }

    function renderHazard(){
      hazard.innerHTML='';
      hazard.style.display=state.showHazard?'grid':'none';
      const n=18; for(let i=0;i<n;i++){const s=document.createElement('span');s.style.background=i%2? 'rgba(255,255,255,.15)':state.accent;hazard.appendChild(s)}
    }

    function renderSegs(){
      segs.innerHTML='';
      for(let i=0;i<state.segments;i++){
        const seg=document.createElement('div'); seg.className='seg';
        const fill=document.createElement('i');
        fill.style.width = (i<state.current? '100%' : i===state.current? '45%':'0');
        seg.appendChild(fill); segs.appendChild(seg);
      }
      counter.textContent=`${Math.min(state.current+1,state.segments)}/${state.segments}`;
    }

    function parallax(delta){
      // delta: -1..1
      const bgShift=12*delta, fgShift=6*delta;
      bgImg.style.transform=`translateX(${bgShift}px)`;
      fgImg.style.transform=`translateX(${fgShift}px)`;
    }

    function next(){ if(state.current<state.segments-1){state.current++; parallax(-0.6); setTimeout(()=>parallax(0),160); renderSegs();} }
    function prev(){ if(state.current>0){state.current--; parallax(0.6); setTimeout(()=>parallax(0),160); renderSegs();} }

    function bind(){
      [['count','count','valueAsNumber'],['current','current','valueAsNumber'],['top','top','valueAsNumber'],
       ['bottom','bottom','valueAsNumber'],['segments','segments','valueAsNumber'],['segH','segH','valueAsNumber'],
       ['segGap','segGap','valueAsNumber'],['hazardH','hazardH','valueAsNumber']].forEach(([id,key,prop])=>{
        const el=document.getElementById(id); el.addEventListener('input',()=>{state[key]=el[prop]; if(key==='current'){state.current=Math.min(state.current,state.segments-1)} renderAll();});
      });
      const accent=$('#accent'); accent.addEventListener('input',()=>{state.accent=accent.value; renderAll()});
      const showHaz=$('#showHazard'); showHaz.addEventListener('change',()=>{state.showHazard=showHaz.checked; renderAll()});
      $('#next').addEventListener('click',next); $('#prev').addEventListener('click',prev);
      $('#zoneR').addEventListener('click',next); $('#zoneL').addEventListener('click',prev);
    }

    function renderAll(){renderOverlay(); renderHazard(); renderSegs();}
    bind(); renderAll();
  </script>
</body>
</html>


