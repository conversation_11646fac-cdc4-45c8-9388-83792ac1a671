<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Progress Bars • SuperDesign Canvas</title>
  <style>
    :root {
      --bg: #0a0a0a;
      --panel: #111418;
      --text: #e7eaf0;
      --muted: #a9b0bb;
      --safety: #ff6600;
      --hi-vis: #ffd335;
      --white-15: rgba(255,255,255,0.15);
      --white-25: rgba(255,255,255,0.25);
    }
    * { box-sizing: border-box; }
    html, body { height: 100%; }
    body {
      margin: 0; background: linear-gradient(180deg, #0b1220, #0a0a0a 30%);
      font: 14px/1.5 system-ui, -apple-system, Segoe UI, Roboto, Inter, sans-serif;
      color: var(--text);
      display: grid; grid-template-columns: 320px 1fr; gap: 24px; padding: 24px;
    }
    aside { background: var(--panel); border: 1px solid var(--white-15); border-radius: 14px; padding: 16px; }
    main  { background: #0c1016; border: 1px solid var(--white-15); border-radius: 14px; padding: 20px; }
    h1 { margin: 0 0 6px; font-size: 18px; }
    h2 { margin: 20px 0 8px; font-size: 14px; color: var(--muted); }
    .row { display: flex; align-items: center; gap: 10px; margin: 8px 0; }
    .row label { width: 120px; color: var(--muted); }
    input[type="range"] { width: 100%; }

    /* Canvas phone frame */
    .phone { width: 390px; height: 844px; margin: 0 auto; border-radius: 28px; overflow: hidden; position: relative; background: #000; box-shadow: 0 10px 40px rgba(0,0,0,.45); }
    .slide { position: absolute; inset: 0; background: #000; display: grid; grid-template-rows: auto 1fr auto; }
    .slide img { width: 100%; height: 100%; object-fit: contain; }
    .img-wrap { position: absolute; inset: 0; display: grid; place-items: center; }

    /* Overlays */
    .overlay { position: absolute; inset: 0; pointer-events: none; background: linear-gradient(to bottom, rgba(0,0,0,var(--top-alpha)) 0%, rgba(0,0,0,0) 55%, rgba(0,0,0,var(--bottom-alpha)) 100%); }

    .top-panel { position: absolute; top: 10px; left: 10px; right: 10px; display: grid; gap: 6px; }
    .hazard { height: var(--hazard-h); display: grid; grid-auto-flow: column; gap: 2px; }
    .hazard span { border-radius: 3px; }

    /* Segmented progress */
    .segments { display: grid; grid-auto-flow: column; gap: var(--seg-gap); align-items: center; }
    .seg { height: var(--seg-h); background: rgba(255,255,255,.22); border-radius: 3px; overflow: hidden; }
    .seg > i { display: block; height: 100%; width: var(--seg-fill, 0%); background: var(--safety, #ff6600); }
    .counter { justify-self: end; color: #fff; font-weight: 600; font-size: 12px; }

    /* Bottom glass bar */
    .glass { position: absolute; left: 16px; right: 16px; bottom: 14px; padding: 10px 12px; backdrop-filter: blur(16px); -webkit-backdrop-filter: blur(16px); background: rgba(0,0,0,.28); border: 1px solid rgba(255,255,255,.15); border-radius: 28px; display: flex; gap: 10px; justify-content: center; }
    .btn { width: 44px; height: 44px; border-radius: 50%; display: grid; place-items: center; border: 1px solid rgba(255,255,255,.15); background: rgba(0,0,0,.35); color: #fff; }

    /* Demo states */
    .row .sw { display: inline-flex; align-items: center; gap: 6px; }
  </style>
</head>
<body>
  <aside>
    <h1>Progress Bar Lab</h1>
    <p style="margin:0 0 10px;color:var(--muted)">Toggle styles and tune values.</p>

    <h2>Design</h2>
    <div class="row"><label>Layout</label>
      <select id="layout">
        <option value="single">A • Single segmented</option>
        <option value="decor+seg">B • Hazard + segmented</option>
        <option value="dual-metric">C • Course (thin) + Module (segmented)</option>
      </select>
    </div>

    <h2>Progress</h2>
    <div class="row"><label>Segments</label><input id="segments" type="range" min="3" max="20" value="12"></div>
    <div class="row"><label>Current</label><input id="current" type="range" min="1" max="12" value="3"></div>

    <h2>Styling</h2>
    <div class="row"><label>Segment height</label><input id="segH" type="range" min="2" max="10" value="4"></div>
    <div class="row"><label>Segment gap</label><input id="segGap" type="range" min="2" max="10" value="6"></div>
    <div class="row"><label>Top overlay</label><input id="topAlpha" type="range" min="0" max="90" value="45"></div>
    <div class="row"><label>Bottom overlay</label><input id="botAlpha" type="range" min="0" max="90" value="60"></div>
    <div class="row"><label>Hazard height</label><input id="hazardH" type="range" min="2" max="14" value="6"></div>
    <div class="row"><label>Accent</label>
      <input type="color" id="accent" value="#ff6600" title="Accent color" />
    </div>

    <h2>About “two progress bars”</h2>
    <ul style="margin:6px 0 0 16px;padding:0 0 0 12px;line-height:1.35;color:var(--muted)">
      <li><b>Decor + Progress</b>: a thin hazard stripe above a real segmented bar.</li>
      <li><b>Dual metric</b>: top thin continuous bar = course/section; bottom segmented = slides in this module.</li>
    </ul>
  </aside>

  <main>
    <div class="phone">
      <div class="slide">
        <div class="img-wrap">
          <img alt="slide" src="https://images.unsplash.com/photo-1505247964246-1f0a90443c36?q=80&w=1600&auto=format&fit=crop"/>
        </div>
        <div class="overlay" id="overlay"></div>

        <div class="top-panel">
          <div class="hazard" id="hazard"></div>
          <div style="display:grid; grid-template-columns: 1fr auto; align-items:center; gap:8px;" id="segRow">
            <div class="segments" id="segmentsBar"></div>
            <div class="counter" id="counter">3/12</div>
          </div>
          <div class="segments" id="courseThin" style="display:none"></div>
        </div>

        <div class="glass">
          <div class="btn">❤</div>
          <div class="btn">🔖</div>
          <div class="btn">⋯</div>
        </div>
      </div>
    </div>
  </main>

  <script>
    const $ = (s)=>document.querySelector(s);
    const hazard = $('#hazard');
    const segmentsBar = $('#segmentsBar');
    const courseThin = $('#courseThin');
    const counter = $('#counter');
    const overlay = $('#overlay');

    const state = {
      layout: 'single',
      segments: 12,
      current: 3,
      segH: 4,
      segGap: 6,
      topAlpha: 45,
      botAlpha: 60,
      hazardH: 6,
      accent: '#ff6600'
    };

    function renderHazard() {
      hazard.innerHTML = '';
      hazard.style.setProperty('--hazard-h', state.hazardH + 'px');
      const n = 18;
      for (let i=0;i<n;i++) {
        const span = document.createElement('span');
        span.style.background = i%2===0 ? state.accent : 'rgba(255,255,255,.15)';
        hazard.appendChild(span);
      }
      hazard.style.display = (state.layout === 'decor+seg') ? 'grid' : 'none';
    }

    function renderSegments() {
      // segmented bar (module/slides)
      segmentsBar.innerHTML = '';
      segmentsBar.style.setProperty('--seg-h', state.segH + 'px');
      segmentsBar.style.setProperty('--seg-gap', state.segGap + 'px');
      segmentsBar.parentElement.style.display = (state.layout === 'dual-metric') ? 'none' : 'grid';
      for (let i=0;i<state.segments;i++) {
        const seg = document.createElement('div');
        seg.className = 'seg';
        const fill = document.createElement('i');
        fill.style.background = state.accent;
        fill.style.width = (i < state.current ? '100%' : i === state.current ? '45%' : '0%');
        seg.appendChild(fill);
        segmentsBar.appendChild(seg);
      }
      counter.textContent = `${Math.min(state.current+1, state.segments)}/${state.segments}`;

      // top thin continuous (course/section)
      courseThin.innerHTML = '';
      courseThin.style.display = (state.layout === 'dual-metric') ? 'grid' : 'none';
      courseThin.style.setProperty('--seg-h', Math.max(2, state.segH-1) + 'px');
      courseThin.style.setProperty('--seg-gap', '0px');
      const thin = document.createElement('div');
      thin.className = 'seg';
      const thinFill = document.createElement('i');
      thinFill.style.background = state.accent;
      thinFill.style.width = ((state.current+1) / state.segments * 100).toFixed(1) + '%';
      thin.appendChild(thinFill);
      courseThin.appendChild(thin);
    }

    function renderOverlay() {
      overlay.style.setProperty('--top-alpha', (state.topAlpha/100).toFixed(2));
      overlay.style.setProperty('--bottom-alpha', (state.botAlpha/100).toFixed(2));
      document.documentElement.style.setProperty('--safety', state.accent);
    }

    function bindUI() {
      const map = [
        ['layout','layout','value'], ['segments','segments','valueAsNumber'], ['current','current','valueAsNumber'],
        ['segH','segH','valueAsNumber'], ['segGap','segGap','valueAsNumber'],
        ['topAlpha','topAlpha','valueAsNumber'], ['botAlpha','botAlpha','valueAsNumber'],
        ['hazardH','hazardH','valueAsNumber'], ['accent','accent','value']
      ];
      map.forEach(([id,key,prop])=>{
        const el = document.getElementById(id);
        el.addEventListener('input', ()=>{ state[key] = el[prop]; if(key==='current'){ state.current=Math.min(state.current,state.segments-1);} renderAll(); });
        if(prop==='valueAsNumber') state[key] = el.valueAsNumber; else state[key]=el.value;
      });
    }

    function renderAll(){ renderOverlay(); renderHazard(); renderSegments(); }
    bindUI(); renderAll();
  </script>
</body>
</html>


