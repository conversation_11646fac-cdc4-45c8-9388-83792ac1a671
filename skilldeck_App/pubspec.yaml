name: skilldeck
description: "RS Plus - Dynamic microlearning app for work zone safety training with interactive swipeable cards."
publish_to: "none"
version: 1.0.3+12

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.0.0
  provider: ^6.1.5
  flutter_animate: ^4.5.2
  card_swiper: ^3.0.1
  http: ^1.2.0
  flutter_secure_storage: ^9.2.2
  webview_flutter: ^4.7.0
  url_launcher: ^6.3.0
  palette_generator: ^0.3.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  background_color_ios: "#000000"
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/icon.png"

flutter:
  uses-material-design: true
  assets:
    - assets/