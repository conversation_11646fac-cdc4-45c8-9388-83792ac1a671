import json
import subprocess

BASE = "https://main.d335csi5l6xsl9.amplifyapp.com"

# Login
login_cmd = f'curl -sS -X POST -H "Content-Type: application/json" -d \'{{"email":"<EMAIL>","password":"<EMAIL>"}}\' "{BASE}/api/users/login"'
login_resp = subprocess.check_output(login_cmd, shell=True).decode()
token = json.loads(login_resp)["token"]

print("=== Testing Course 21 with depth=1 ===")
course_cmd = f'curl -sS -H "Authorization: JWT {token}" "{BASE}/api/courses/21?depth=1"'
course_resp = subprocess.check_output(course_cmd, shell=True).decode()
course = json.loads(course_resp)

modules = course.get("modules", [])
mod33 = [m for m in modules if m.get("id") == 33]
if mod33:
    slides = mod33[0].get("slides", [])
    print(f"Module 33 has {len(slides)} slides")
    if slides:
        first_slide = slides[0]
        print(f"First slide type: {type(first_slide).__name__}")
        print(f"First slide: {first_slide}")
        if isinstance(first_slide, dict):
            print(f"First slide image field: {first_slide.get('image')}")

print("\n=== Testing bulk slides endpoint ===")
slides_cmd = f'curl -sS -H "Authorization: JWT {token}" "{BASE}/api/slides?where%5Bid%5D%5Bin%5D=2783&depth=1"'
slides_resp = subprocess.check_output(slides_cmd, shell=True).decode()
slides_data = json.loads(slides_resp)
if slides_data.get("docs"):
    slide = slides_data["docs"][0]
    print(f"Slide 2783 from bulk endpoint:")
    print(f"  - image type: {type(slide.get('image')).__name__}")
    print(f"  - image value: {slide.get('image')}")
    if isinstance(slide.get('image'), dict):
        print(f"  - image url: {slide['image'].get('url')}")
