.PHONY: help clean get build-dev build-staging build-prod run-dev run-staging run-prod test analyze format

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Available targets:'
	@awk 'BEGIN {FS = ":.*##"; printf "\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  %-20s %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

clean: ## Clean build artifacts
	flutter clean
	rm -rf build/
	rm -rf .dart_tool/
	rm -rf .packages

get: ## Get dependencies
	flutter pub get

build-dev: ## Build development APK
	flutter build apk --release --flavor development -t lib/main_development.dart

build-staging: ## Build staging APK
	flutter build apk --release --flavor staging -t lib/main_staging.dart

build-prod: ## Build production APK
	flutter build apk --release --flavor production -t lib/main_production.dart

build-ios-dev: ## Build development iOS
	flutter build ios --release --flavor development -t lib/main_development.dart

build-ios-staging: ## Build staging iOS
	flutter build ios --release --flavor staging -t lib/main_staging.dart

build-ios-prod: ## Build production iOS
	flutter build ios --release --flavor production -t lib/main_production.dart

run-dev: ## Run development flavor
	flutter run --flavor development -t lib/main_development.dart

run-staging: ## Run staging flavor
	flutter run --flavor staging -t lib/main_staging.dart

run-prod: ## Run production flavor
	flutter run --flavor production -t lib/main_production.dart

test: ## Run tests
	flutter test

analyze: ## Analyze code
	flutter analyze

format: ## Format code
	dart format lib/ --line-length=100

watch: ## Watch for changes and hot reload (development)
	flutter run --flavor development -t lib/main_development.dart --hot