import 'package:flutter/material.dart';

enum Flavor {
  development,
  staging,
  production,
}

class FlavorConfig {
  final Flavor flavor;
  final String name;
  final String apiBaseUrl;
  final bool enableLogging;
  final bool enablePerformanceOverlay;
  final String appTitle;
  
  static FlavorConfig? _instance;
  
  FlavorConfig._internal({
    required this.flavor,
    required this.name,
    required this.apiBaseUrl,
    required this.enableLogging,
    required this.enablePerformanceOverlay,
    required this.appTitle,
  });
  
  static FlavorConfig get instance {
    _instance ??= FlavorConfig._internal(
      flavor: Flavor.development,
      name: 'Development',
      apiBaseUrl: 'https://dev-api.skilldeck.com',
      enableLogging: true,
      enablePerformanceOverlay: false,
      appTitle: 'SkillDeck Dev',
    );
    return _instance!;
  }
  
  static void initialize({required Flavor flavor}) {
    switch (flavor) {
      case Flavor.development:
        _instance = FlavorConfig._internal(
          flavor: flavor,
          name: 'Development',
          apiBaseUrl: 'https://dev-api.skilldeck.com',
          enableLogging: true,
          enablePerformanceOverlay: false,
          appTitle: 'SkillDeck Dev',
        );
        break;
      case Flavor.staging:
        _instance = FlavorConfig._internal(
          flavor: flavor,
          name: 'Staging',
          apiBaseUrl: 'https://staging-api.skilldeck.com',
          enableLogging: true,
          enablePerformanceOverlay: false,
          appTitle: 'SkillDeck Staging',
        );
        break;
      case Flavor.production:
        _instance = FlavorConfig._internal(
          flavor: flavor,
          name: 'Production',
          apiBaseUrl: 'https://api.skilldeck.com',
          enableLogging: false,
          enablePerformanceOverlay: false,
          appTitle: 'SkillDeck',
        );
        break;
    }
  }
  
  bool get isDevelopment => flavor == Flavor.development;
  bool get isStaging => flavor == Flavor.staging;
  bool get isProduction => flavor == Flavor.production;
  
  Color get primaryColor {
    switch (flavor) {
      case Flavor.development:
        return Colors.blue;
      case Flavor.staging:
        return Colors.orange;
      case Flavor.production:
        return const Color(0xFFFF6600);
    }
  }
  
  Widget flavorBanner({required Widget child}) {
    if (isProduction) return child;
    
    return Stack(
      children: [
        child,
        _buildBanner(),
      ],
    );
  }
  
  Widget _buildBanner() {
    return Positioned(
      top: 50,
      left: 0,
      child: Container(
        decoration: BoxDecoration(
          color: primaryColor.withValues(alpha: 0.9),
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(8),
            bottomRight: Radius.circular(8),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Text(
          name.toUpperCase(),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}