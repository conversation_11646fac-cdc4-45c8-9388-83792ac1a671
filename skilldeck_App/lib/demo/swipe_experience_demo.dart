import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/widgets/enhanced_swipe_slides.dart';

/// Demo page showcasing the enhanced swipe experience
/// This demonstrates all the modern swipe features implemented
class SwipeExperienceDemo extends StatefulWidget {
  const SwipeExperienceDemo({super.key});

  @override
  State<SwipeExperienceDemo> createState() => _SwipeExperienceDemoState();
}

class _SwipeExperienceDemoState extends State<SwipeExperienceDemo> {
  int currentSlideIndex = 0;
  bool showVerticalMode = false;
  
  // Demo slides data
  final List<SlideResponse> demoSlides = [
    SlideResponse(
      id: '1',
      title: 'Welcome to Enhanced Swipes',
      content: 'Experience smooth, responsive swipe gestures with momentum scrolling and visual feedback.',
      imageUrl: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=600&fit=crop',
    ),
    SlideResponse(
      id: '2', 
      title: 'Touch Responsiveness',
      content: 'Feel the immediate haptic feedback and see real-time visual responses to your touch.',
      imageUrl: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop',
    ),
    SlideResponse(
      id: '3',
      title: 'Momentum Scrolling',
      content: 'Natural deceleration when you release your finger, just like modern social media apps.',
      imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
    ),
    SlideResponse(
      id: '4',
      title: 'Snap to Position',
      content: 'Slides automatically snap to perfect positions with smooth animations.',
      imageUrl: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=800&h=600&fit=crop',
    ),
    SlideResponse(
      id: '5',
      title: 'Visual Indicators',
      content: 'Beautiful progress indicators show your current position in the slide deck.',
      imageUrl: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=800&h=600&fit=crop',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Enhanced Swipe Demo',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                showVerticalMode = !showVerticalMode;
              });
              HapticFeedback.selectionClick();
            },
            icon: Icon(
              showVerticalMode ? Icons.swap_horiz : Icons.swap_vert,
              color: Colors.white,
            ),
            tooltip: showVerticalMode ? 'Switch to Horizontal' : 'Switch to Vertical',
          ),
        ],
      ),
      body: Column(
        children: [
          // Mode indicator
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.safetyOrange.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.safetyOrange.withValues(alpha: 0.5),
              ),
            ),
            child: Text(
              showVerticalMode ? 'Vertical Swipe Mode' : 'Horizontal Swipe Mode',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ).animate().fadeIn().slideY(begin: -0.3),
          
          // Enhanced swipe slides
          Expanded(
            child: EnhancedSwipeSlides(
              slides: demoSlides,
              primaryDirection: showVerticalMode 
                  ? SwipeDirection.vertical 
                  : SwipeDirection.horizontal,
              enableHorizontalSwipe: !showVerticalMode,
              enableVerticalSwipe: showVerticalMode,
              onSlideChanged: (index) {
                setState(() {
                  currentSlideIndex = index;
                });
                HapticFeedback.selectionClick();
              },
              onSlideCompleted: (index) {
                debugPrint('Slide $index completed');
              },
            ),
          ),
          
          // Current slide info
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.safetyOrange,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Slide ${currentSlideIndex + 1} of ${demoSlides.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        demoSlides[currentSlideIndex].title ?? 'Untitled',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ).animate().fadeIn(delay: 200.ms).slideY(begin: 0.3),
          
          // Feature highlights
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Enhanced Features:',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                _buildFeatureItem('🎯', 'Smooth swipe gestures'),
                _buildFeatureItem('📱', 'Touch responsiveness with haptic feedback'),
                _buildFeatureItem('🚀', 'Momentum scrolling'),
                _buildFeatureItem('📍', 'Snap-to-position'),
                _buildFeatureItem('💫', 'Visual indicators & progress'),
                _buildFeatureItem('🎨', 'Smooth animations with easing curves'),
              ],
            ),
          ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.3),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 12)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Extension to add sample data to SlideResponse for demo purposes
extension SlideResponseDemo on SlideResponse {
  static SlideResponse create({
    required String id,
    required String title,
    required String content,
    required String imageUrl,
  }) {
    return SlideResponse(
      id: id,
      title: title,
      content: content,
      imageUrl: imageUrl,
    );
  }
}
