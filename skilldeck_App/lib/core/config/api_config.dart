import 'package:skilldeck/config/flavor_config.dart';

class ApiConfig {
  // Use flavor-based base URL so all environments are centralized in FlavorConfig
  static String get baseUrl => FlavorConfig.instance.apiBaseUrl;
  static const String apiPath = '/api';
  
  // API Endpoints
  static const String loginEndpoint = '/users/login';
  static const String logoutEndpoint = '/users/logout';
  static const String coursesEndpoint = '/courses';
  static const String modulesEndpoint = '/modules';
  static const String slidesEndpoint = '/slides';
  
  // Progress endpoints (when available)
  static const String progressModuleEndpoint = '/mobile/progress/module';
  static const String progressCourseEndpoint = '/mobile/progress/course';
  static const String progressSlideEndpoint = '/mobile/progress/slide';
  
  // Test credentials (REMOVE IN PRODUCTION)
  static const String testEmail = '<EMAIL>';
  static const String testPassword = '<EMAIL>';
  
  static String get apiBaseUrl => '$baseUrl$apiPath';
  
  static String getMediaUrl(String? relativePath) {
    if (relativePath == null || relativePath.isEmpty) {
      return '';
    }
    if (relativePath.startsWith('http')) {
      return relativePath;
    }
    // Payload may return paths starting with /api/media/... which are served by the same origin
    return '$baseUrl$relativePath';
  }
}