class UserProgress {
  final String userId;
  final Map<String, CourseProgress> courseProgress;

  const UserProgress({
    required this.userId,
    required this.courseProgress,
  });

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'courseProgress': courseProgress.map(
      (key, value) => MapEntry(key, value.toJson()),
    ),
  };

  factory UserProgress.fromJson(Map<String, dynamic> json) => UserProgress(
    userId: json['userId'],
    courseProgress: (json['courseProgress'] as Map<String, dynamic>).map(
      (key, value) => MapEntry(key, CourseProgress.fromJson(value)),
    ),
  );
}

class CourseProgress {
  final String courseId;
  final Map<String, ModuleProgress> moduleProgress;
  final DateTime? lastAccessed;

  const CourseProgress({
    required this.courseId,
    required this.moduleProgress,
    this.lastAccessed,
  });

  int get completedModules => 
      moduleProgress.values.where((m) => m.isCompleted).length;

  bool get isCompleted => 
      moduleProgress.isNotEmpty && 
      moduleProgress.values.every((m) => m.isCompleted);

  double get completionPercentage {
    if (moduleProgress.isEmpty) return 0.0;
    final totalCompleted = moduleProgress.values
        .map((m) => m.completionPercentage)
        .fold(0.0, (sum, percent) => sum + percent);
    return totalCompleted / moduleProgress.length;
  }

  Map<String, dynamic> toJson() => {
    'courseId': courseId,
    'moduleProgress': moduleProgress.map(
      (key, value) => MapEntry(key, value.toJson()),
    ),
    'lastAccessed': lastAccessed?.toIso8601String(),
  };

  factory CourseProgress.fromJson(Map<String, dynamic> json) => CourseProgress(
    courseId: json['courseId'],
    moduleProgress: (json['moduleProgress'] as Map<String, dynamic>).map(
      (key, value) => MapEntry(key, ModuleProgress.fromJson(value)),
    ),
    lastAccessed: json['lastAccessed'] != null
        ? DateTime.parse(json['lastAccessed'])
        : null,
  );
}

class ModuleProgress {
  final String moduleId;
  final Set<String> completedCardIds;
  final int currentCardIndex;
  final DateTime? lastAccessed;

  const ModuleProgress({
    required this.moduleId,
    required this.completedCardIds,
    this.currentCardIndex = 0,
    this.lastAccessed,
  });

  bool get isCompleted => currentCardIndex >= 0 && completedCardIds.isNotEmpty;

  double get completionPercentage {
    if (completedCardIds.isEmpty) return 0.0;
    return completedCardIds.length / (currentCardIndex + 1).clamp(1, 100);
  }

  Map<String, dynamic> toJson() => {
    'moduleId': moduleId,
    'completedCardIds': completedCardIds.toList(),
    'currentCardIndex': currentCardIndex,
    'lastAccessed': lastAccessed?.toIso8601String(),
  };

  factory ModuleProgress.fromJson(Map<String, dynamic> json) => ModuleProgress(
    moduleId: json['moduleId'],
    completedCardIds: Set<String>.from(json['completedCardIds'] ?? []),
    currentCardIndex: json['currentCardIndex'] ?? 0,
    lastAccessed: json['lastAccessed'] != null
        ? DateTime.parse(json['lastAccessed'])
        : null,
  );
}