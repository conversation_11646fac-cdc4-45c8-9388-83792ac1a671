import '../core/config/api_config.dart';

// Course Response Model
class CourseResponse {
  final String id;
  final String title;
  final String? description;
  final dynamic modules; // Can be List<String> or List<ModuleResponse>
  final MediaResponse? courseThumbnail;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  
  CourseResponse({
    required this.id,
    required this.title,
    this.description,
    this.modules,
    this.courseThumbnail,
    this.createdAt,
    this.updatedAt,
  });
  
  factory CourseResponse.fromJson(Map<String, dynamic> json) {
    return CourseResponse(
      id: (json['id'] ?? '').toString(),
      title: json['title'] ?? '',
      description: json['description'],
      modules: json['modules'], // Keep raw, process based on depth
      courseThumbnail: (() {
        final thumb = json['courseThumbnail'];
        if (thumb is Map<String, dynamic>) {
          return MediaResponse.fromJson(thumb);
        }
        // If the API returns a numeric/string ID, we skip parsing and leave null
        return null;
      })(),
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
    );
  }
  
  int get moduleCount {
    if (modules == null) return 0;
    if (modules is List) return (modules as List).length;
    return 0;
  }
  
  List<String> get moduleIds {
    if (modules == null) return [];
    if (modules is List) {
      return (modules as List).map<String>((m) {
        if (m is String) return m;
        if (m is num) return m.toString();
        if (m is Map) return (m['id'] ?? '').toString();
        return '';
      }).where((id) => id.isNotEmpty).toList();
    }
    return [];
  }
  
  List<ModuleResponse> get expandedModules {
    if (modules == null) return [];
    if (modules is List) {
      return (modules as List).whereType<Map>().map((m) {
        return ModuleResponse.fromJson(m as Map<String, dynamic>);
      }).toList();
    }
    return [];
  }
}

// Module Response Model
class ModuleResponse {
  final String id;
  final String title;
  final String? description;
  final dynamic slides; // Can be List<String> or List<SlideResponse>
  final MediaResponse? moduleThumbnail;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  
  ModuleResponse({
    required this.id,
    required this.title,
    this.description,
    this.slides,
    this.moduleThumbnail,
    this.createdAt,
    this.updatedAt,
  });
  
  factory ModuleResponse.fromJson(Map<String, dynamic> json) {
    return ModuleResponse(
      id: (json['id'] ?? '').toString(),
      title: json['title'] ?? '',
      description: json['description'],
      slides: json['slides'], // Keep raw, process based on depth
      moduleThumbnail: (() {
        final thumb = json['moduleThumbnail'];
        if (thumb is Map<String, dynamic>) {
          return MediaResponse.fromJson(thumb);
        }
        return null;
      })(),
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
    );
  }
  
  int get slideCount {
    if (slides == null) return 0;
    if (slides is List) return (slides as List).length;
    return 0;
  }
  
  List<String> get slideIds {
    if (slides == null) return [];
    if (slides is List) {
      return (slides as List).map<String>((s) {
        if (s is String) return s;
        if (s is num) return s.toString();
        if (s is Map) return (s['id'] ?? '').toString();
        return '';
      }).where((id) => id.isNotEmpty).toList();
    }
    return [];
  }
  
  List<SlideResponse> get expandedSlides {
    if (slides == null) return [];
    if (slides is List) {
      return (slides as List).whereType<Map>().map((s) {
        return SlideResponse.fromJson(s as Map<String, dynamic>);
      }).toList();
    }
    return [];
  }
}

// Slide Response Model
class SlideResponse {
  final String id;
  final String title;
  final String? content;
  final String? type; // 'content', 'quiz', 'interaction', 'summary'
  final MediaResponse? image;
  final Map<String, dynamic>? quizData;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  
  SlideResponse({
    required this.id,
    required this.title,
    this.content,
    this.type,
    this.image,
    this.quizData,
    this.createdAt,
    this.updatedAt,
  });
  
  factory SlideResponse.fromJson(Map<String, dynamic> json) {
    return SlideResponse(
      id: (json['id'] ?? '').toString(),
      title: (json['title'] ?? '').toString(),
      content: ((json['content'] ?? json['description'])?.toString()),
      type: (json['type']?.toString()) ?? 'content',
      image: (() {
        final img = json['image'];
        if (img is Map<String, dynamic>) {
          return MediaResponse.fromJson(img);
        }
        // When API returns a numeric ID instead of an expanded object, leave null
        return null;
      })(),
      quizData: json['quizData'] is Map<String, dynamic> ? json['quizData'] : null,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'].toString())
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'].toString())
          : null,
    );
  }
}

// Media Response Model
class MediaResponse {
  final String? id;
  final String? url;
  final Map<String, dynamic>? sizes;
  final String? filename;
  final String? mimeType;
  final int? filesize;
  final int? width;
  final int? height;
  
  MediaResponse({
    this.id,
    this.url,
    this.sizes,
    this.filename,
    this.mimeType,
    this.filesize,
    this.width,
    this.height,
  });
  
  factory MediaResponse.fromJson(Map<String, dynamic> json) {
    return MediaResponse(
      id: json['id']?.toString(),
      url: json['url'],
      sizes: json['sizes'] is Map<String, dynamic> ? (json['sizes'] as Map<String, dynamic>) : null,
      filename: json['filename'],
      mimeType: json['mimeType'],
      filesize: json['filesize'] is int ? json['filesize'] : null,
      width: json['width'] is int ? json['width'] : null,
      height: json['height'] is int ? json['height'] : null,
    );
  }
  
  String get fullUrl {
    // Prefer card size, then thumbnail, then original URL
    String? candidateUrl = url;
    if (sizes != null) {
      final card = sizes!['card'];
      final thumb = sizes!['thumbnail'];
      if (card is Map && (card['url'] is String)) candidateUrl = card['url'] as String;
      else if (thumb is Map && (thumb['url'] is String)) candidateUrl = thumb['url'] as String;
    }
    return ApiConfig.getMediaUrl(candidateUrl);
  }

  // Always return the original uploaded asset URL (no size variants)
  String get originalFullUrl => ApiConfig.getMediaUrl(url);
}

// Progress Models
class ModuleProgress {
  final String moduleId;
  final int completedSlides;
  final int totalSlides;
  
  ModuleProgress({
    required this.moduleId,
    required this.completedSlides,
    required this.totalSlides,
  });
  
  factory ModuleProgress.fromJson(Map<String, dynamic> json) {
    return ModuleProgress(
      moduleId: json['moduleId'] ?? '',
      completedSlides: json['completedSlides'] ?? 0,
      totalSlides: json['totalSlides'] ?? 0,
    );
  }
  
  double get progressPercentage {
    if (totalSlides == 0) return 0;
    return (completedSlides / totalSlides) * 100;
  }
}

class CourseProgress {
  final String courseId;
  final int completedModules;
  final int totalModules;
  
  CourseProgress({
    required this.courseId,
    required this.completedModules,
    required this.totalModules,
  });
  
  factory CourseProgress.fromJson(Map<String, dynamic> json) {
    return CourseProgress(
      courseId: json['courseId'] ?? '',
      completedModules: json['completedModules'] ?? 0,
      totalModules: json['totalModules'] ?? 0,
    );
  }
  
  double get progressPercentage {
    if (totalModules == 0) return 0;
    return (completedModules / totalModules) * 100;
  }
}