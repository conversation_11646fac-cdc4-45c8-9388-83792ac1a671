import 'package:skilldeck/models/learning_card.dart';

class Module {
  final String id;
  final String title;
  final String description;
  final List<LearningCard> cards;
  final String? iconName;

  const Mo<PERSON><PERSON>({
    required this.id,
    required this.title,
    required this.description,
    required this.cards,
    this.iconName,
  });

  int get totalCards => cards.length;

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'cards': cards.map((card) => card.toJson()).toList(),
    'iconName': iconName,
  };

  factory Module.fromJson(Map<String, dynamic> json) => Module(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    cards: (json['cards'] as List)
        .map((card) => LearningCard.fromJson(card))
        .toList(),
    iconName: json['iconName'],
  );
}