class LearningCard {
  final String id;
  final String title;
  final String content;
  final String? imageUrl;
  final List<String>? bulletPoints;
  final String? backContent;
  final CardType type;

  const LearningCard({
    required this.id,
    required this.title,
    required this.content,
    this.imageUrl,
    this.bulletPoints,
    this.backContent,
    this.type = CardType.content,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'content': content,
    'imageUrl': imageUrl,
    'bulletPoints': bulletPoints,
    'backContent': backContent,
    'type': type.toString(),
  };

  factory LearningCard.fromJson(Map<String, dynamic> json) => LearningCard(
    id: json['id'],
    title: json['title'],
    content: json['content'],
    imageUrl: json['imageUrl'],
    bulletPoints: json['bulletPoints']?.cast<String>(),
    backContent: json['backContent'],
    type: CardType.values.firstWhere(
      (e) => e.toString() == json['type'],
      orElse: () => CardType.content,
    ),
  );
}

enum CardType {
  content,
  quiz,
  interaction,
  summary,
}