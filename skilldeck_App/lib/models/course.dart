import 'package:skilldeck/models/module.dart';

class Course {
  final String id;
  final String title;
  final String description;
  final List<Module> modules;
  final String? imageUrl;

  const Course({
    required this.id,
    required this.title,
    required this.description,
    required this.modules,
    this.imageUrl,
  });

  int get totalModules => modules.length;
  int get totalCards => modules.fold(0, (sum, module) => sum + module.totalCards);

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'modules': modules.map((module) => module.toJson()).toList(),
    'imageUrl': imageUrl,
  };

  factory Course.fromJson(Map<String, dynamic> json) => Course(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    modules: (json['modules'] as List)
        .map((module) => Module.fromJson(module))
        .toList(),
    imageUrl: json['imageUrl'],
  );
}