import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skilldeck/config/flavor_config.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/providers/theme_provider.dart';
import 'package:skilldeck/screens/home_page_clean.dart';

class SkillDeckApp extends StatelessWidget {
  const SkillDeckApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          return MaterialApp(
            title: FlavorConfig.instance.appTitle,
            debugShowCheckedModeBanner: !FlavorConfig.instance.isProduction,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const HomePageClean(),
          );
        },
      ),
    );
  }
}