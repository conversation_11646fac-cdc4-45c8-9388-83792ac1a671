import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:skilldeck/config/flavor_config.dart';
import 'package:skilldeck/app.dart';

void main() {
  runZonedGuarded(() {
    WidgetsFlutterBinding.ensureInitialized();

    // Global error logging for framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
    };

    // Global error logging for uncaught async errors
    PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
      debugPrint('Uncaught error: $error');
      debugPrint('$stack');
      return true; // error handled
    };

    FlavorConfig.initialize(flavor: Flavor.development);
    runApp(const SkillDeckApp());
  }, (error, stack) {
    debugPrint('Zoned error: $error');
    debugPrint('$stack');
  });
}