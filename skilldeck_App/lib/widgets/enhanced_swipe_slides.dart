import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/core/theme/app_theme.dart';

/// Enhanced swipe slide component with modern social media-like experience
class EnhancedSwipeSlides extends StatefulWidget {
  final List<SlideResponse> slides;
  final Function(int index)? onSlideChanged;
  final Function(int index)? onSlideCompleted;
  final bool enableVerticalSwipe;
  final bool enableHorizontalSwipe;
  final SwipeDirection primaryDirection;
  
  const EnhancedSwipeSlides({
    super.key,
    required this.slides,
    this.onSlideChanged,
    this.onSlideCompleted,
    this.enableVerticalSwipe = true,
    this.enableHorizontalSwipe = true,
    this.primaryDirection = SwipeDirection.horizontal,
  });

  @override
  State<EnhancedSwipeSlides> createState() => _EnhancedSwipeSlidesState();
}

enum SwipeDirection { horizontal, vertical }

class _EnhancedSwipeSlidesState extends State<EnhancedSwipeSlides>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _swipeAnimationController;
  late AnimationController _progressController;
  late Animation<double> _swipeAnimation;
  
  int currentIndex = 0;
  double _swipeProgress = 0.0;
  bool _isSwipeInProgress = false;
  Offset _panStart = Offset.zero;
  Offset _panUpdate = Offset.zero;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    // Animation controller for smooth swipe transitions
    _swipeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // Progress animation controller
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    // Swipe animation with custom curve for natural feel
    _swipeAnimation = CurvedAnimation(
      parent: _swipeAnimationController,
      curve: Curves.easeOutCubic,
    );
    
    _progressController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _swipeAnimationController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    _panStart = details.localPosition;
    _isSwipeInProgress = true;
    _swipeAnimationController.stop();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isSwipeInProgress) return;
    
    _panUpdate = details.localPosition;
    final delta = _panUpdate - _panStart;
    final screenSize = MediaQuery.of(context).size;
    
    double progress = 0.0;
    
    if (widget.primaryDirection == SwipeDirection.horizontal && widget.enableHorizontalSwipe) {
      progress = -delta.dx / screenSize.width;
    } else if (widget.primaryDirection == SwipeDirection.vertical && widget.enableVerticalSwipe) {
      progress = -delta.dy / screenSize.height;
    }
    
    // Clamp progress and add resistance at boundaries
    if (currentIndex == 0 && progress < 0) {
      progress = progress * 0.3; // Resistance when trying to go before first slide
    } else if (currentIndex == widget.slides.length - 1 && progress > 0) {
      progress = progress * 0.3; // Resistance when trying to go after last slide
    }
    
    setState(() {
      _swipeProgress = progress.clamp(-1.0, 1.0);
    });
    
    // Provide subtle haptic feedback during swipe
    if (_swipeProgress.abs() > 0.3) {
      HapticFeedback.selectionClick();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isSwipeInProgress) return;
    
    _isSwipeInProgress = false;
    final velocity = widget.primaryDirection == SwipeDirection.horizontal
        ? details.velocity.pixelsPerSecond.dx
        : details.velocity.pixelsPerSecond.dy;
    
    // Determine if swipe should trigger navigation
    final shouldNavigate = _swipeProgress.abs() > 0.25 || velocity.abs() > 500;
    
    if (shouldNavigate) {
      final direction = _swipeProgress > 0 ? 1 : -1;
      _navigateToSlide(currentIndex + direction, withMomentum: velocity.abs() > 500);
    } else {
      // Snap back to current position
      _snapToPosition();
    }
  }

  void _navigateToSlide(int targetIndex, {bool withMomentum = false}) {
    if (targetIndex < 0 || targetIndex >= widget.slides.length) {
      _snapToPosition();
      return;
    }
    
    // Enhanced haptic feedback for navigation
    HapticFeedback.mediumImpact();
    
    setState(() {
      currentIndex = targetIndex;
    });
    
    // Animate to new position with momentum-based duration
    final duration = withMomentum 
        ? const Duration(milliseconds: 200)
        : const Duration(milliseconds: 300);
    
    _pageController.animateToPage(
      targetIndex,
      duration: duration,
      curve: withMomentum ? Curves.easeOut : Curves.easeOutCubic,
    );
    
    // Reset swipe progress
    _swipeProgress = 0.0;
    _swipeAnimationController.forward();
    
    // Notify callbacks
    widget.onSlideChanged?.call(targetIndex);
    widget.onSlideCompleted?.call(targetIndex);
  }

  void _snapToPosition() {
    _swipeAnimationController.forward();
    setState(() {
      _swipeProgress = 0.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Main slide content with gesture detection
          GestureDetector(
            onPanStart: _onPanStart,
            onPanUpdate: _onPanUpdate,
            onPanEnd: _onPanEnd,
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  currentIndex = index;
                });
                widget.onSlideChanged?.call(index);
              },
              itemCount: widget.slides.length,
              itemBuilder: (context, index) {
                return _buildSlideWithParallax(index);
              },
            ),
          ),
          
          // Progress indicators
          _buildProgressIndicators(),
          
          // Navigation hints
          _buildNavigationHints(),
        ],
      ),
    );
  }

  Widget _buildSlideWithParallax(int index) {
    return AnimatedBuilder(
      animation: _swipeAnimation,
      builder: (context, child) {
        // Calculate parallax offset based on swipe progress
        final parallaxOffset = _swipeProgress * 50;
        
        return Transform.translate(
          offset: widget.primaryDirection == SwipeDirection.horizontal
              ? Offset(parallaxOffset, 0)
              : Offset(0, parallaxOffset),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.3),
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
            child: _buildSlideContent(widget.slides[index]),
          ),
        );
      },
    );
  }

  Widget _buildSlideContent(SlideResponse slide) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Background image with parallax
        if (slide.image?.fullUrl != null && slide.image!.fullUrl.isNotEmpty)
          Image.network(
            slide.image!.fullUrl,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
        
        // Content overlay
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (slide.title.isNotEmpty)
                Text(
                  slide.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ).animate().fadeIn(delay: 200.ms).slideY(begin: 0.3),
              
              const SizedBox(height: 12),
              
              if (slide.content?.isNotEmpty == true)
                Text(
                  slide.content!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.3),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicators() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 20,
      left: 20,
      right: 20,
      child: Row(
        children: List.generate(widget.slides.length, (index) {
          final isActive = index == currentIndex;
          final isPast = index < currentIndex;
          
          return Expanded(
            child: Container(
              height: 3,
              margin: EdgeInsets.only(right: index == widget.slides.length - 1 ? 0 : 4),
              decoration: BoxDecoration(
                color: isPast || isActive 
                    ? AppColors.safetyOrange 
                    : Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ).animate().scaleX(
              duration: 300.ms,
              curve: Curves.easeOut,
            ),
          );
        }),
      ),
    );
  }

  Widget _buildNavigationHints() {
    return Positioned(
      bottom: 40,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (currentIndex > 0)
            _buildHintButton(
              icon: widget.primaryDirection == SwipeDirection.horizontal 
                  ? Icons.arrow_back_ios 
                  : Icons.keyboard_arrow_up,
              onTap: () => _navigateToSlide(currentIndex - 1),
            ),
          
          const SizedBox(width: 40),
          
          if (currentIndex < widget.slides.length - 1)
            _buildHintButton(
              icon: widget.primaryDirection == SwipeDirection.horizontal 
                  ? Icons.arrow_forward_ios 
                  : Icons.keyboard_arrow_down,
              onTap: () => _navigateToSlide(currentIndex + 1),
            ),
        ],
      ),
    );
  }

  Widget _buildHintButton({required IconData icon, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    ).animate().fadeIn().scale();
  }
}
