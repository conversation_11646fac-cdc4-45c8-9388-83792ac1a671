import 'package:flutter/material.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/core/theme/app_theme.dart';

class CompactProgressIndicators extends StatefulWidget {
  final VoidCallback? onRefresh;
  const CompactProgressIndicators({super.key, this.onRefresh});

  @override
  State<CompactProgressIndicators> createState() => _CompactProgressIndicatorsState();
}

class _CompactProgressIndicatorsState extends State<CompactProgressIndicators> {
  final ProgressService _progressService = ProgressService();
  final SupabaseRsService _supabaseService = SupabaseRsService();
  
  int _completedSlides = 0;
  int _totalCourses = 0;
  int _completedCourses = 0;
  int _totalModules = 0;
  int _completedModules = 0;
  int _totalSlides = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProgressData();
  }

  @override
  void didUpdateWidget(covariant CompactProgressIndicators oldWidget) {
    super.didUpdateWidget(oldWidget);
    _loadProgressData();
  }

  Future<void> _loadProgressData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    
    try {
      final userProgress = await _progressService.getUserProgress();
      if (!mounted) return;
      
      int totalSlides = 0;
      int completedSlides = 0;
      int totalModules = 0;
      int completedModules = 0;
      int totalCourses = 0;
      int completedCourses = 0;

      final coursesRes = await _supabaseService.getCourses(limit: 100);
      if (!mounted) return;
      
      if (coursesRes.success && coursesRes.data != null) {
        totalCourses = coursesRes.data!.length;
        for (final course in coursesRes.data!) {
          if (!mounted) return;
          
          final courseDetail = await _supabaseService.getCourseDetail(course.id);
          if (!mounted) return;
          
          if (courseDetail.success && courseDetail.data != null) {
            final modules = courseDetail.data!.expandedModules;
            totalModules += modules.length;

            int courseCompletedModules = 0;

            for (final module in modules) {
              if (!mounted) return;
              
              final moduleSlideCount = module.slideCount;
              totalSlides += moduleSlideCount;

              final courseProgress = userProgress.courseProgress[course.id];
              if (courseProgress != null) {
                final moduleProgress = courseProgress.moduleProgress[module.id];
                if (moduleProgress != null) {
                  final completedCount = moduleProgress.completedCardIds.length;
                  completedSlides += completedCount;
                  
                  if (moduleSlideCount > 0 && completedCount >= moduleSlideCount) {
                    completedModules++;
                    courseCompletedModules++;
                  }
                }
              }
            }

            if (modules.isNotEmpty && courseCompletedModules == modules.length) {
              completedCourses++;
            }
          }
        }
      }

      if (!mounted) return;
      setState(() {
        _completedSlides = completedSlides;
        _totalCourses = totalCourses;
        _completedCourses = completedCourses;
        _totalModules = totalModules;
        _completedModules = completedModules;
        _totalSlides = totalSlides;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
        height: 60,
        child: Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
              ),
            ),
          ),
        ),
      );
    }

    final coursePct = _totalCourses > 0 ? _completedCourses / _totalCourses : 0.0;
    final modulePct = _totalModules > 0 ? _completedModules / _totalModules : 0.0;
    final practicePct = _totalSlides > 0 ? _completedSlides / _totalSlides : 0.0;

    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark 
            ? const Color(0xFF1A1F2E).withValues(alpha: 0.3)
            : const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: isDark ? 0.08 : 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Progress bars
          _buildMinimalProgressBar(
            context,
            label: 'Courses',
            progress: coursePct,
            color: AppColors.constructionGreen,
            value: '$_completedCourses/$_totalCourses',
          ),
          const SizedBox(height: 12),
          _buildMinimalProgressBar(
            context,
            label: 'Modules',
            progress: modulePct,
            color: AppColors.highVisYellow,
            value: '$_completedModules/$_totalModules',
          ),
          const SizedBox(height: 12),
          _buildMinimalProgressBar(
            context,
            label: 'Practice',
            progress: practicePct,
            color: AppColors.industrialBlue,
            value: '$_completedSlides/$_totalSlides',
          ),
        ],
      ),
    );
  }

  Widget _buildMinimalProgressBar(BuildContext context, {
    required String label,
    required double progress,
    required Color color,
    required String value,
  }) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        // Label
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Progress bar
        Expanded(
          child: Container(
            height: 6,
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progress.clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Value and percentage
        SizedBox(
          width: 50,
          child: Text(
            value,
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 30,
          child: Text(
            '${(progress * 100).toInt()}%',
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ),
      ],
    );
  }
}