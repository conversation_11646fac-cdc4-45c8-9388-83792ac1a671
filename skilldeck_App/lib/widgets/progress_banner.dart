import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:skilldeck/widgets/progress_ring.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/screens/module_detail_page_api.dart';
import 'package:skilldeck/core/theme/app_theme.dart';

class ProgressBanner extends StatefulWidget {
  final VoidCallback? onQuickStart;
  
  const ProgressBanner({
    super.key,
    this.onQuickStart,
  });

  @override
  State<ProgressBanner> createState() => _ProgressBannerState();
}

class _ProgressBannerState extends State<ProgressBanner> {
  final ProgressService _progressService = ProgressService();
  final SupabaseRsService _supabaseService = SupabaseRsService();
  
  double _overallProgress = 0;
  int _totalModules = 0;
  int _completedModules = 0;
  int _totalSlides = 0;
  int _completedSlides = 0;
  String? _lastCourseId;
  String? _lastModuleId;
  ModuleResponse? _lastModule;
  bool _isLoading = true;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadProgressData();
  }

  Future<void> _loadProgressData() async {
    setState(() => _isLoading = true);
    
    try {
      final userProgress = await _progressService.getUserProgress();
      
      int totalModules = 0;
      int completedModules = 0;
      int totalSlides = 0;
      int completedSlides = 0;
      DateTime? mostRecentAccess;
      String? lastCourseId;
      String? lastModuleId;
      
      // Get all courses to calculate totals
      final coursesRes = await _supabaseService.getCourses(limit: 100);
      if (coursesRes.success && coursesRes.data != null) {
        for (final course in coursesRes.data!) {
          final courseDetail = await _supabaseService.getCourseDetail(course.id);
          if (courseDetail.success && courseDetail.data != null) {
            final modules = courseDetail.data!.expandedModules;
            totalModules += modules.length;
            
            for (final module in modules) {
              totalSlides += module.slideCount;
              
              // Check progress for this module
              final courseProgress = userProgress.courseProgress[course.id];
              if (courseProgress != null) {
                final moduleProgress = courseProgress.moduleProgress[module.id];
                if (moduleProgress != null) {
                  completedSlides += moduleProgress.completedCardIds.length;
                  
                  // Check if module is completed
                  if (moduleProgress.completedCardIds.length >= module.slideCount && 
                      module.slideCount > 0) {
                    completedModules++;
                  }
                  
                  // Track most recent activity
                  if (moduleProgress.lastAccessed != null) {
                    if (mostRecentAccess == null || 
                        moduleProgress.lastAccessed!.isAfter(mostRecentAccess)) {
                      mostRecentAccess = moduleProgress.lastAccessed;
                      lastCourseId = course.id;
                      lastModuleId = module.id;
                      _lastModule = module;
                    }
                  }
                }
              }
            }
          }
        }
      }
      
      setState(() {
        _totalModules = totalModules;
        _completedModules = completedModules;
        _totalSlides = totalSlides;
        _completedSlides = completedSlides;
        _overallProgress = totalSlides > 0 ? completedSlides / totalSlides : 0;
        _lastCourseId = lastCourseId;
        _lastModuleId = lastModuleId;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  void _handleQuickStart() {
    HapticFeedback.lightImpact();
    if (_lastCourseId != null && _lastModuleId != null && _lastModule != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => ModuleDetailPageApi(
            courseId: _lastCourseId!,
            module: _lastModule!,
          ),
        ),
      ).then((_) => _loadProgressData());
    } else if (widget.onQuickStart != null) {
      widget.onQuickStart!();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        height: 135,
        color: const Color(0xFF1E293B),
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        setState(() => _isExpanded = !_isExpanded);
        HapticFeedback.lightImpact();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: _isExpanded ? 270 : 135,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF1E293B),
              const Color(0xFF334155),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Main progress section
            Container(
              height: 135,
              padding: const EdgeInsets.fromLTRB(20, 8, 20, 8),
              child: Row(
                children: [
                  // Overall progress ring
                  ProgressRing(
                    progress: _overallProgress,
                    label: 'Overall',
                    color: AppColors.constructionGreen,
                    icon: Icons.school,
                    size: 65,
                  ),
                  const SizedBox(width: 24),
                  // Stats and quick start
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Progress',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$_completedSlides of $_totalSlides slides completed',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (_lastModuleId != null)
                          ElevatedButton.icon(
                            onPressed: _handleQuickStart,
                            icon: const Icon(Icons.play_arrow, size: 18),
                            label: const Text('Continue Learning'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.safetyOrange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  // Expand indicator
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                ],
              ),
            ),
            // Expanded details
            if (_isExpanded)
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: Colors.white.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Progress rings for different categories
                      ProgressRingGroup(
                        ringSize: 60,
                        rings: [
                          ProgressRingData(
                            progress: _totalModules > 0 
                                ? _completedModules / _totalModules 
                                : 0,
                            label: 'Modules',
                            color: AppColors.highVisYellow,
                            icon: Icons.view_module,
                          ),
                          ProgressRingData(
                            progress: _completedSlides > 0 ? 0.8 : 0, // Mock streak
                            label: 'Streak',
                            color: AppColors.warningRed,
                            icon: Icons.local_fire_department,
                          ),
                          ProgressRingData(
                            progress: _overallProgress,
                            label: 'Mastery',
                            color: AppColors.industrialBlue,
                            icon: Icons.star,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // Stats row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildStat('Modules', '$_completedModules/$_totalModules'),
                          _buildStat('Slides', '$_completedSlides/$_totalSlides'),
                          _buildStat('Time', '2.5h'), // Mock time
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}