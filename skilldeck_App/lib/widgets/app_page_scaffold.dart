import 'package:flutter/material.dart';
import 'package:skilldeck/widgets/app_sliver_header.dart';

/// A consistent page scaffold that ensures uniform layout across all pages
class AppPageScaffold extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? searchBar;
  final List<Widget> slivers;
  final bool showSettings;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final EdgeInsets contentPadding;
  // Optional widget rendered below the header and above the search bar
  final Widget? headerBelow;
  // Optional pull-to-refresh handler; when provided, the scroll view is wrapped in a RefreshIndicator
  final Future<void> Function()? onRefresh;

  const AppPageScaffold({
    super.key,
    required this.title,
    this.subtitle,
    this.searchBar,
    required this.slivers,
    this.showSettings = true,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.contentPadding = const EdgeInsets.symmetric(horizontal: 24),
    this.headerBelow,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final scrollView = CustomScrollView(
      physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
      slivers: [
        // Consistent header across all pages
        AppSliverHeader(
          title: title,
          subtitle: subtitle,
          showSettings: showSettings,
        ),
        // Optional content directly under the header (e.g., key stat banner)
        if (headerBelow != null)
          SliverToBoxAdapter(
            child: Padding(
               padding: contentPadding.copyWith(top: 12, bottom: 0),
              child: headerBelow,
            ),
          ),
        
        // Search bar with consistent spacing
        if (searchBar != null)
          SliverToBoxAdapter(
            child: Padding(
               padding: contentPadding.copyWith(top: 16, bottom: 24),
              child: searchBar,
            ),
          ),
        
         // Content slivers
        ...slivers,
        
        // Bottom spacing for nav bar
        if (bottomNavigationBar != null)
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
      ],
    );

    return Scaffold(
      backgroundColor: isDark 
          ? const Color(0xFF111827)  // Slightly lighter than header for contrast
          : const Color(0xFFF9FAFB),  // Light gray for light theme
      body: onRefresh != null
          ? RefreshIndicator(
              onRefresh: onRefresh!,
              child: scrollView,
            )
          : scrollView,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
    );
  }
}

/// Consistent search bar widget used across pages
class AppSearchBar extends StatelessWidget {
  final String hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;

  const AppSearchBar({
    super.key,
    this.hintText = 'Search...',
    this.controller,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDark 
            ? const Color(0xFF2D3748)  // Darker surface for dark theme
            : const Color(0xFFF7FAFC),  // Light gray for light theme
        // Match radius with progress banner for consistent look
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          // Unify border strength with other top widgets
          color: theme.colorScheme.outline.withValues(alpha: 0.12),
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        onTap: onTap,
        readOnly: readOnly,
        style: theme.textTheme.bodyMedium,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 150 / 255),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 150 / 255),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }
}

/// Section header widget for consistent section titles
class AppSectionHeader extends StatelessWidget {
  final String title;
  final String? actionText;
  final VoidCallback? onActionTap;

  const AppSectionHeader({
    super.key,
    required this.title,
    this.actionText,
    this.onActionTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          if (actionText != null)
            GestureDetector(
              onTap: onActionTap,
              child: Text(
                actionText!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
        ],
      ),
    );
  }
}