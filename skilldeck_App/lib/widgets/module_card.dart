import 'package:flutter/material.dart';
import 'package:skilldeck/models/module.dart';
import 'package:skilldeck/models/user_progress.dart';
import 'package:skilldeck/core/theme/app_theme.dart';

class ModuleCard extends StatelessWidget {
  final Module module;
  final ModuleProgress? progress;
  final VoidCallback onTap;

  const ModuleCard({
    super.key,
    required this.module,
    this.progress,
    required this.onTap,
  });

  IconData _getModuleIcon(String? iconName) {
    switch (iconName) {
      case 'bedtime':
        return Icons.bedtime;
      case 'warning':
        return Icons.warning_amber;
      case 'lightbulb':
        return Icons.lightbulb_outline;
      case 'traffic':
        return Icons.traffic;
      case 'flag':
        return Icons.flag;
      case 'electrical_services':
        return Icons.electrical_services;
      default:
        return Icons.layers;
    }
  }

  String _getStatusText() {
    if (progress == null || progress!.completedCardIds.isEmpty) {
      return 'Start';
    } else if (progress!.isCompleted) {
      return 'Completed';
    } else {
      return '${progress!.completionPercentage.toStringAsFixed(0)}%';
    }
  }

  Color _getStatusColor() {
    if (progress == null || progress!.completedCardIds.isEmpty) {
      // Use safety orange for a professional work-zone accent when not started
      return AppColors.safetyOrange;
    } else if (progress!.isCompleted) {
      return AppColors.constructionGreen;
    } else {
      return AppColors.highVisYellow;
    }
  }

  IconData _getStatusIcon() {
    if (progress == null || progress!.completedCardIds.isEmpty) {
      return Icons.play_arrow;
    } else if (progress!.isCompleted) {
      return Icons.check_circle;
    } else {
      return Icons.schedule;
    }
  }

  @override
  Widget build(BuildContext context) {
    final completedCards = progress?.completedCardIds.length ?? 0;
    final statusColor = _getStatusColor();

    return Card(
      elevation: 0,
      color: AppColors.darkSurface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          // Subtle neutral outline for a cleaner, more professional look
          color: AppColors.textOnDark.withValues(alpha: 0.06),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(20),
          // Solid surface; avoid gradients for a more enterprise feel
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: AppColors.darkSurface,
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.12),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: statusColor.withValues(alpha: 0.25),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      _getModuleIcon(module.iconName),
                      size: 28,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          module.title,
                          style: AppTextStyles.headlineSmall.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textOnDark,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              _getStatusIcon(),
                              size: 16,
                              color: statusColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _getStatusText(),
                              style: AppTextStyles.labelMedium.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              '$completedCards of ${module.totalCards} cards',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.textSecondaryOnDark,
                              ),
                            ),
                            const Spacer(),
                            Icon(
                              Icons.navigate_next,
                              color: AppColors.textSecondaryOnDark,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (progress != null && progress!.completedCardIds.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  height: 8,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: statusColor.withValues(alpha: 0.2),
                  ),
                  child: LinearProgressIndicator(
                    value: progress!.completionPercentage,
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                    borderRadius: BorderRadius.circular(8),
                    minHeight: 8,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}