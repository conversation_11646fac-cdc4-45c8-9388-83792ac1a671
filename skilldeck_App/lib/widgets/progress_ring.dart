import 'package:flutter/material.dart';
import 'dart:math' as math;

class ProgressRing extends StatelessWidget {
  final double progress;
  final String label;
  final Color color;
  final double size;
  final IconData icon;
  final bool showPercentage;

  const ProgressRing({
    super.key,
    required this.progress,
    required this.label,
    required this.color,
    this.size = 80,
    required this.icon,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size + 30, // Add more space for label to prevent overflow
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: Stack(
            children: [
              // Background circle
              SizedBox(
                width: size,
                height: size,
                child: CustomPaint(
                  painter: RingPainter(
                    progress: 1.0,
                    color: color.withValues(alpha: 0.2),
                    strokeWidth: size * 0.08,
                  ),
                ),
              ),
              // Progress arc
              SizedBox(
                width: size,
                height: size,
                child: CustomPaint(
                  painter: Ring<PERSON>ain<PERSON>(
                    progress: progress,
                    color: color,
                    strokeWidth: size * 0.08,
                  ),
                ),
              ),
              // Center content
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      icon,
                      size: size * 0.25,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    if (showPercentage) ...[
                      const SizedBox(height: 2),
                      Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: size * 0.175,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

class RingPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  RingPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw arc from -90 degrees (top)
    const startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * progress;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(RingPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.color != color ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}

class ProgressRingGroup extends StatelessWidget {
  final List<ProgressRingData> rings;
  final double ringSize;

  const ProgressRingGroup({
    super.key,
    required this.rings,
    this.ringSize = 70,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: rings.map((ring) => ProgressRing(
        progress: ring.progress,
        label: ring.label,
        color: ring.color,
        icon: ring.icon,
        size: ringSize,
      )).toList(),
    );
  }
}

class ProgressRingData {
  final double progress;
  final String label;
  final Color color;
  final IconData icon;

  ProgressRingData({
    required this.progress,
    required this.label,
    required this.color,
    required this.icon,
  });
}