import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:skilldeck/screens/settings_page.dart';

class AppSliverHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showSettings;
  final Widget? customAction;

  const AppSliverHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.showSettings = true,
    this.customAction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Professional, solid header color (slightly darker light blue in light mode)
    final Color headerColor = isDark
        ? const Color(0xFF111827)
        : const Color(0xFF3B82F6); // slightly darker light blue

    return SliverAppBar(
      pinned: true,
      floating: false,
      centerTitle: false,
      scrolledUnderElevation: 0,
      backgroundColor: headerColor,
      surfaceTintColor: Colors.transparent,
      toolbarHeight: subtitle != null ? 86.0 : 66.0,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.light,
      ),
      titleSpacing: 24,
      title: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              letterSpacing: 0,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle!,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white.withValues(alpha: 0.85),
              ),
            ),
          ],
        ],
      ),
      actions: [
        if (customAction != null) customAction!,
        if (showSettings)
          IconButton(
            tooltip: 'Settings',
            icon: const Icon(
              Icons.settings_outlined,
              color: Colors.white,
            ),
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const SettingsPage()),
            ),
          ),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Container(
          height: 1,
          color: isDark 
              ? Colors.white.withValues(alpha: 0.06)
              : Colors.black.withValues(alpha: 0.06),
        ),
      ),
    );
  }
}
