import 'package:flutter/material.dart';
import 'package:skilldeck/core/theme/app_theme.dart';

class ProgressTracker extends StatelessWidget {
  final int currentIndex;
  final int totalItems;
  final int completedCount;
  final String itemType; // 'slide', 'card', etc.
  final bool showPercentage;
  final bool showCompletedCount;

  const ProgressTracker({
    super.key,
    required this.currentIndex,
    required this.totalItems,
    required this.completedCount,
    this.itemType = 'slide',
    this.showPercentage = true,
    this.showCompletedCount = true,
  });

  @override
  Widget build(BuildContext context) {
    final progressPercentage = totalItems > 0 ? (completedCount / totalItems * 100).round() : 0;
    final currentPosition = currentIndex + 1;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.constructionGreen.withValues(alpha: 30 / 255),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.constructionGreen.withValues(alpha: 50 / 255),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 14,
            color: AppColors.constructionGreen,
          ),
          const SizedBox(width: 6),
          Text(
            _buildProgressText(currentPosition, totalItems, completedCount, progressPercentage),
            style: TextStyle(
              color: AppColors.constructionGreen,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _buildProgressText(int current, int total, int completed, int percentage) {
    if (completed == total) {
      // All completed - show completion status
      return 'Complete';
    } else {
      // Show current position and completion separately
      return '$current/$total • $completed completed';
    }
  }
}

class ProgressBar extends StatelessWidget {
  final int completedCount;
  final int totalItems;
  final Color? progressColor;
  final Color? backgroundColor;
  final double height;

  const ProgressBar({
    super.key,
    required this.completedCount,
    required this.totalItems,
    this.progressColor,
    this.backgroundColor,
    this.height = 4,
  });

  @override
  Widget build(BuildContext context) {
    final progress = totalItems > 0 ? completedCount / totalItems : 0.0;
    
    return Container(
      height: height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(height / 2),
        child: LinearProgressIndicator(
          value: progress,
      backgroundColor: backgroundColor ?? Colors.white.withValues(alpha: 40 / 255),
          valueColor: AlwaysStoppedAnimation<Color>(
            progressColor ?? AppColors.constructionGreen,
          ),
        ),
      ),
    );
  }
}

class SlideIndicators extends StatelessWidget {
  final int currentIndex;
  final int totalSlides;
  final Set<String> completedSlideIds;
  final List<String> slideIds;
  final int maxIndicators;

  const SlideIndicators({
    super.key,
    required this.currentIndex,
    required this.totalSlides,
    required this.completedSlideIds,
    required this.slideIds,
    this.maxIndicators = 10,
  });

  @override
  Widget build(BuildContext context) {
    if (totalSlides > maxIndicators) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalSlides,
        (index) {
          final slideId = index < slideIds.length ? slideIds[index] : '';
          final isCompleted = completedSlideIds.contains(slideId);
          final isCurrent = index == currentIndex;
          
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 3),
            width: isCurrent ? 8 : 6,
            height: 6,
            decoration: BoxDecoration(
              color: isCompleted
                  ? AppColors.constructionGreen
                  : isCurrent
                      ? AppColors.safetyOrange
                      : Colors.white.withValues(alpha: 75 / 255),
              borderRadius: BorderRadius.circular(3),
            ),
          );
        },
      ),
    );
  }
}