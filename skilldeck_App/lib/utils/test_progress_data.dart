import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/models/user_progress.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';

/// Test utility to add sample progress data for testing
/// Call this from a button or debug menu to populate test data
class TestProgressData {
  static Future<void> addSampleProgress() async {
    final progressService = ProgressService();
    final supabaseService = SupabaseRsService();
    
    try {
      // Get courses first
      final coursesRes = await supabaseService.getCourses(limit: 10);
      if (!coursesRes.success || coursesRes.data == null) {
        print('Could not fetch courses');
        return;
      }
      
      final courses = coursesRes.data!;
      if (courses.isEmpty) {
        print('No courses available');
        return;
      }
      
      // Take first course and add some progress
      final firstCourse = courses.first;
      final courseDetail = await supabaseService.getCourseDetail(firstCourse.id);
      
      if (!courseDetail.success || courseDetail.data == null) {
        print('Could not fetch course detail');
        return;
      }
      
      final modules = courseDetail.data!.expandedModules;
      if (modules.isEmpty) {
        print('No modules in course');
        return;
      }
      
      // Simulate completing first module's slides
      final firstModule = modules.first;
      final slidesRes = await supabaseService.getSlides(firstModule.slides);
      
      if (!slidesRes.success || slidesRes.data == null) {
        print('Could not fetch slides');
        return;
      }
      
      final slides = slidesRes.data!;
      
      // Mark some slides as complete (e.g., first 3 slides)
      for (int i = 0; i < slides.length && i < 3; i++) {
        await progressService.markCardComplete(
          courseId: firstCourse.id,
          moduleId: firstModule.id,
          cardId: slides[i].id,
        );
      }
      
      // If there's a second module, mark one slide complete there too
      if (modules.length > 1) {
        final secondModule = modules[1];
        final secondSlidesRes = await supabaseService.getSlides(secondModule.slides);
        
        if (secondSlidesRes.success && secondSlidesRes.data != null && secondSlidesRes.data!.isNotEmpty) {
          await progressService.markCardComplete(
            courseId: firstCourse.id,
            moduleId: secondModule.id,
            cardId: secondSlidesRes.data!.first.id,
          );
        }
      }
      
      print('Sample progress data added successfully');
      print('Course: ${firstCourse.title}');
      print('Module: ${firstModule.title}');
      print('Slides completed: ${slides.length > 3 ? 3 : slides.length}');
      
    } catch (e) {
      print('Error adding sample progress: $e');
    }
  }
  
  static Future<void> clearAllProgress() async {
    final progressService = ProgressService();
    await progressService.saveUserProgress(UserProgress(
      userId: 'default_user',
      courseProgress: {},
    ));
    print('All progress cleared');
  }
}