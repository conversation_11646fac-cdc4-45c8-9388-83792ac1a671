import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/widgets/progress_tracker.dart';

class ModuleSlidesSwiper extends StatefulWidget {
  final String courseId;
  final ModuleResponse module;
  final List<SlideResponse> slides;

  const ModuleSlidesSwiper({
    super.key,
    required this.courseId,
    required this.module,
    required this.slides,
  });

  @override
  State<ModuleSlidesSwiper> createState() => _ModuleSlidesSwiperState();
}

class _ModuleSlidesSwiperState extends State<ModuleSlidesSwiper>
    with TickerProviderStateMixin {
  late SwiperController _swiperController;
  late AnimationController _progressController;
  late AnimationController _cardFlipController;
  late AnimationController _swipeAnimationController;
  late AnimationController _momentumController;
  final ProgressService _progressService = ProgressService();

  int currentIndex = 0;
  bool showBack = false;
  Set<int> viewedCards = {};
  Set<String> completedSlides = {};

  // Enhanced swipe state
  double _swipeProgress = 0.0;
  bool _isSwipeInProgress = false;
  Offset _lastPanPosition = Offset.zero;
  
  @override
  void initState() {
    super.initState();
    _swiperController = SwiperController();
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _cardFlipController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _swipeAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _momentumController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    viewedCards.add(0);
    _progressController.forward();
    _loadProgress();
  }
  
  Future<void> _loadProgress() async {
    try {
      final moduleProgress = await _progressService.getModuleProgress(
        widget.courseId,
        widget.module.id,
      );
      
      if (mounted) {
        // Filter completed slides to only include ones that exist in current slides
        final validCompletedSlides = moduleProgress.completedCardIds
            .where((slideId) => widget.slides.any((slide) => slide.id == slideId))
            .toSet();
        
        setState(() {
          completedSlides = validCompletedSlides;
          // Resume from last position
          final savedIndex = moduleProgress.currentCardIndex;
          if (savedIndex > 0 && savedIndex < widget.slides.length) {
            currentIndex = savedIndex;
            _swiperController.move(currentIndex);
          }
        });
        
        // Debug logging
        debugPrint('[PROGRESS] Loaded progress: ${validCompletedSlides.length}/${widget.slides.length}');
        debugPrint('[PROGRESS] Completed slide IDs: $validCompletedSlides');
        debugPrint('[PROGRESS] Available slide IDs: ${widget.slides.map((s) => s.id).toList()}');
      }
    } catch (e) {
      debugPrint('[PROGRESS] Error loading progress: $e');
    }
  }

  @override
  void dispose() {
    _swiperController.dispose();
    _progressController.dispose();
    _cardFlipController.dispose();
    _swipeAnimationController.dispose();
    _momentumController.dispose();
    super.dispose();
  }

  // Enhanced gesture handlers for modern swipe experience
  void _onPanStart(DragStartDetails details) {
    _isSwipeInProgress = true;
    _lastPanPosition = details.localPosition;
    _swipeAnimationController.stop();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isSwipeInProgress) return;

    final delta = details.localPosition - _lastPanPosition;
    final screenWidth = MediaQuery.of(context).size.width;

    setState(() {
      _swipeProgress = (delta.dx / screenWidth).clamp(-1.0, 1.0);
    });

    // Provide subtle haptic feedback during swipe
    if (_swipeProgress.abs() > 0.3) {
      HapticFeedback.selectionClick();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isSwipeInProgress) return;

    _isSwipeInProgress = false;
    final velocity = details.velocity.pixelsPerSecond.dx;

    // Enhanced momentum detection
    if (_swipeProgress.abs() > 0.25 || velocity.abs() > 800) {
      final direction = _swipeProgress > 0 ? -1 : 1; // Inverted for natural feel
      final targetIndex = (currentIndex + direction).clamp(0, widget.slides.length - 1);

      if (targetIndex != currentIndex) {
        // Enhanced haptic feedback for navigation
        HapticFeedback.mediumImpact();
        _swiperController.move(targetIndex);
      }
    }

    // Reset swipe state
    setState(() {
      _swipeProgress = 0.0;
    });
    _swipeAnimationController.forward();
  }

  // Flipping disabled per request

  void _onSwipe(int index) async {
    if (!mounted) return;
    
    // Provide immediate haptic feedback
    HapticFeedback.selectionClick();
    
    final wasAlreadyViewed = viewedCards.contains(index);
    
    setState(() {
      currentIndex = index;
      viewedCards.add(index);
      showBack = false;
    });
    
    // Reset animations
    _cardFlipController.reset();
    _progressController.forward();
    
    try {
      // Save current position
      await _progressService.updateCurrentCardIndex(
        courseId: widget.courseId,
        moduleId: widget.module.id,
        cardIndex: index,
      );
      
      // Mark slide as completed only when first viewed (forward progress)
      if (index < widget.slides.length && !wasAlreadyViewed) {
        final slide = widget.slides[index];
        final slideId = slide.id;
        
        debugPrint('[PROGRESS] First time viewing slide $index: $slideId');
        
        if (!completedSlides.contains(slideId)) {
          if (mounted) {
            setState(() {
              completedSlides.add(slideId);
            });
          }
          
          await _progressService.markSlideComplete(
            courseId: widget.courseId,
            moduleId: widget.module.id,
            slideId: slideId,
          );
          
          // Debug logging
          final completedCount = widget.slides.where((s) => completedSlides.contains(s.id)).length;
          debugPrint('[PROGRESS] Slide completed: $slideId');
          debugPrint('[PROGRESS] Total completed: $completedCount/${widget.slides.length}');
          debugPrint('[PROGRESS] Progress: ${(completedCount / widget.slides.length * 100).round()}%');
          
          // Provide completion feedback
          HapticFeedback.lightImpact();
        }
      } else if (wasAlreadyViewed) {
        debugPrint('[PROGRESS] Returning to previously viewed slide $index');
      }
    } catch (e) {
      debugPrint('Error updating progress: $e');
    }
  }

  void _completeModule() {
    HapticFeedback.mediumImpact();
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Module completed! Great job! 🎉'),
        backgroundColor: AppColors.constructionGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showZoomedImage(String imageUrl) {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog.fullscreen(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                panEnabled: true,
                scaleEnabled: true,
                minScale: 0.5,
                maxScale: 4.0,
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: 64,
                        color: Colors.white30,
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 50,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF0F172A),
            const Color(0xFF1E293B),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: CustomPaint(
                  painter: _BackgroundPatternPainter(),
                ),
              ),
              
              Column(
                children: [
                  // Header
                  _buildHeader(),
                  
                  // Progress Bar
                  _buildProgressBar(),
                  
                  // Cards
                  Expanded(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        final cardWidth = constraints.maxWidth * 0.86;
                        final cardHeight = constraints.maxHeight * 0.82;
                        return GestureDetector(
                          onPanStart: _onPanStart,
                          onPanUpdate: _onPanUpdate,
                          onPanEnd: _onPanEnd,
                          child: Swiper(
                            controller: _swiperController,
                            itemCount: widget.slides.length,
                            onIndexChanged: _onSwipe,
                            index: currentIndex,
                            loop: false,
                            // Enhanced swipe settings for modern social media feel
                            duration: 250, // Faster for more responsive feel
                            curve: Curves.easeOutCubic, // More natural easing
                            viewportFraction: 0.92, // Slightly larger for better immersion
                            scale: 0.94, // Subtle scale for depth
                            itemWidth: cardWidth,
                            itemHeight: cardHeight,
                            // Enhanced physics for momentum scrolling
                            physics: const BouncingScrollPhysics(
                              parent: AlwaysScrollableScrollPhysics(),
                            ),
                            // Enhanced transformer for smooth parallax
                            transformer: ScaleAndFadeTransformer(),
                            itemBuilder: (context, index) {
                              return _buildSwipeCard(widget.slides[index], cardWidth, cardHeight);
                            },
                            layout: SwiperLayout.STACK,
                      // Enhanced pagination with smooth animations
                      pagination: widget.slides.length <= 5 ? SwiperPagination(
                        alignment: Alignment.bottomCenter,
                        margin: const EdgeInsets.only(bottom: 80),
                        builder: DotSwiperPaginationBuilder(
                          color: Colors.white.withValues(alpha: 0.3),
                          activeColor: AppColors.safetyOrange,
                          size: 8,
                          activeSize: 12,
                          space: 6,
                        ),
                      ) : null,
                          ),
                        );
                      },
                    ),
                  ),
                  
                  // Navigation
                  _buildNavigation(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final totalSlides = widget.slides.length;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white, size: 20),
            onPressed: () => Navigator.of(context).pop(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.module.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 12),
          // Compact progress pill (always visible)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.18), width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.slideshow, size: 14, color: Colors.white.withValues(alpha: 0.9)),
                const SizedBox(width: 6),
                Text('${currentIndex + 1} / $totalSlides',
                    style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w600)),
                const SizedBox(width: 8),
                Container(
                  width: 40,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: totalSlides == 0 ? 0 : (currentIndex + 1) / totalSlides,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.safetyOrange,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: -0.2, end: 0);
  }

  Widget _buildProgressBar() {
    final totalSlides = widget.slides.length;
    final completedCount = widget.slides.where((slide) => completedSlides.contains(slide.id)).length;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Clean progress bar
          ProgressBar(
            completedCount: completedCount,
            totalItems: totalSlides,
          ),
          const SizedBox(height: 12),
          // Slide indicators (only if 10 or fewer slides)
          SlideIndicators(
            currentIndex: currentIndex,
            totalSlides: totalSlides,
            completedSlideIds: completedSlides,
            slideIds: widget.slides.map((s) => s.id).toList(),
          ),
        ],
      ),
    ).animate(controller: _progressController)
      .fadeIn(duration: 500.ms)
      .slideY(begin: -0.1, end: 0);
  }

  // Legacy card builder removed in full-bleed mode

  Widget _buildCardFront(SlideResponse slide) { // kept for clarity, referenced via full-bleed path
    if (slide.image?.fullUrl != null) {
      // Keep for legacy card mode (not used in full-bleed path)
      return _buildFullBleedSlide(slide);
    }
    
    // Fallback for slides without images
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
             AppColors.safetyOrange,
             AppColors.safetyOrange.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getSlideIcon(slide.type),
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: 24),
          Text(
            slide.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (slide.content != null) ...[
            const SizedBox(height: 16),
            Icon(
              Icons.touch_app,
                 color: Colors.white.withValues(alpha: 0.7),
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Tap to read more',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // New: Full-bleed slide renderer (no white card, no cropping)
  Widget _buildFullBleedSlide(SlideResponse slide) {
    if (slide.image?.fullUrl == null) {
      return Center(
        child: Icon(
          Icons.image_not_supported,
          size: 64,
          color: Colors.white.withValues(alpha: 0.4),
        ),
      );
    }
    final url = slide.image!.originalFullUrl.isNotEmpty
        ? slide.image!.originalFullUrl
        : slide.image!.fullUrl;
    return GestureDetector(
      onDoubleTap: () => _showZoomedImage(url),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Soft, blurred backdrop using the same image (eliminates harsh black bars)
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Image.network(
              url,
              fit: BoxFit.cover,
            ),
          ),
          // Dim overlay to bring focus to the foreground image
          Container(color: Colors.black.withValues(alpha: 0.35)),
          // Foreground full image (not cropped)
          Center(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.35),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              clipBehavior: Clip.antiAlias,
              child: Image.network(
                url,
                fit: BoxFit.contain,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.image_not_supported,
                    size: 64,
                    color: Colors.white.withValues(alpha: 0.4),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced swipe card with modern visual effects and animations
  Widget _buildSwipeCard(SlideResponse slide, double width, double height) {
    final url = (slide.image?.originalFullUrl.isNotEmpty ?? false)
        ? slide.image!.originalFullUrl
        : (slide.image?.fullUrl ?? "");

    return AnimatedBuilder(
      animation: _swipeAnimationController,
      builder: (context, child) {
        // Calculate dynamic effects based on swipe progress
        final swipeOffset = _swipeProgress * 20;
        final shadowIntensity = (1.0 - _swipeProgress.abs()).clamp(0.0, 1.0);
        final scaleEffect = 1.0 - (_swipeProgress.abs() * 0.02);

        return Transform.translate(
          offset: Offset(swipeOffset, 0),
          child: Transform.scale(
            scale: scaleEffect,
            child: Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                border: Border.all(color: Colors.black.withValues(alpha: 0.06), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.25 * shadowIntensity),
                    blurRadius: 20 + (10 * _swipeProgress.abs()),
                    offset: Offset(swipeOffset * 0.5, 10 + (5 * _swipeProgress.abs())),
                  ),
                ],
              ),
              clipBehavior: Clip.antiAlias,
              child: Stack(
                children: [
                  // Main content
                  url.isEmpty
                      ? Center(
                          child: Icon(
                            Icons.image_not_supported,
                            size: 48,
                            color: Colors.black.withValues(alpha: 0.2),
                          ),
                        )
                      : GestureDetector(
                          onDoubleTap: () => _showZoomedImage(url),
                          child: Container(
                            color: Colors.black,
                            child: Image.network(
                              url,
                              fit: BoxFit.contain,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          ),
                        ),

                  // Add subtle overlay during swipe for visual feedback
                  if (_isSwipeInProgress)
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: _swipeProgress > 0 ? Alignment.centerLeft : Alignment.centerRight,
                          end: _swipeProgress > 0 ? Alignment.centerRight : Alignment.centerLeft,
                          colors: [
                            Colors.transparent,
                            Colors.white.withValues(alpha: 0.1 * _swipeProgress.abs()),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ).animate().fadeIn(duration: 300.ms).scale(
          begin: const Offset(0.95, 0.95),
          duration: 300.ms,
          curve: Curves.easeOutCubic,
        );
      },
    );
  }

  // Card back removed (flip disabled)

  Widget _buildNavigation() {
    final isLastCard = currentIndex == widget.slides.length - 1;
    final currentSlideId = widget.slides[currentIndex].id;
    final isCurrentSlideCompleted = completedSlides.contains(currentSlideId);
    // Check if all slides are actually completed by checking each slide ID
    final allSlidesCompleted = widget.slides.every((slide) => completedSlides.contains(slide.id));
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Slide completion status
          if (!isCurrentSlideCompleted) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.highVisYellow.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.highVisYellow.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.visibility_outlined,
                    size: 14,
                    color: AppColors.highVisYellow,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Viewing slide...',
                    style: TextStyle(
                      color: AppColors.highVisYellow,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
          
          // Navigation buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Previous button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: currentIndex > 0
                      ? () {
                          HapticFeedback.lightImpact();
                          _swiperController.previous();
                        }
                      : null,
                  icon: Icon(
                    Icons.arrow_back_ios,
                    size: 18,
                    color: currentIndex > 0
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.3),
                  ),
                ),
              ),
              
              // Main action button
              ElevatedButton(
                onPressed: isLastCard && allSlidesCompleted
                    ? _completeModule
                    : () {
                        HapticFeedback.lightImpact();
                        if (isLastCard) {
                          _completeModule();
                        } else {
                          _swiperController.next();
                        }
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isLastCard && allSlidesCompleted
                      ? AppColors.constructionGreen
                      : AppColors.safetyOrange,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 4,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isLastCard && allSlidesCompleted
                          ? 'Complete Module'
                          : isLastCard
                              ? 'Finish Review'
                              : 'Next Slide',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      isLastCard && allSlidesCompleted
                          ? Icons.check_circle
                          : isLastCard
                              ? Icons.refresh
                              : Icons.arrow_forward,
                      size: 18,
                      color: Colors.white,
                    ),
                  ],
                ),
              ).animate()
                .scale(delay: 300.ms, duration: 400.ms)
                .fadeIn(),
              
              // Skip/Jump to end button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: !isLastCard
                      ? () {
                          HapticFeedback.lightImpact();
                          _swiperController.move(widget.slides.length - 1);
                        }
                      : null,
                  icon: Icon(
                    Icons.skip_next,
                    size: 18,
                    color: !isLastCard
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.3),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.2, end: 0);
  }

  IconData _getSlideIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'quiz':
        return Icons.quiz;
      case 'interaction':
        return Icons.touch_app;
      case 'summary':
        return Icons.summarize;
      default:
        return Icons.article;
    }
  }
}

class _BackgroundPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.03)
      ..style = PaintingStyle.fill;

    const spacing = 30.0;
    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), 2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
