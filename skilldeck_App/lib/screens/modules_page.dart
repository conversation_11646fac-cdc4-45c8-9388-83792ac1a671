import 'package:flutter/material.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/widgets/app_page_scaffold.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/screens/module_detail_page_api.dart';

class ModulesPage extends StatefulWidget {
  const ModulesPage({super.key});

  @override
  State<ModulesPage> createState() => _ModulesPageState();
}

class _ModulesPageState extends State<ModulesPage> {
  final TextEditingController _searchController = TextEditingController();
  final SupabaseRsService _supa = SupabaseRsService();
  final ProgressService _progressService = ProgressService();
  
  List<ModuleResponse> _allModules = [];
  List<ModuleResponse> _filteredModules = [];
  Map<String, int> _moduleSlideCounts = {};
  Map<String, int> _moduleCompletedCounts = {};
  Map<String, String> _moduleToCourseMap = {}; // Maps module ID to course ID
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_handleSearch);
    _loadModules();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _handleSearch() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredModules = _allModules;
      } else {
        _filteredModules = _allModules.where((module) {
          return module.title.toLowerCase().contains(query) ||
                 (module.description?.toLowerCase().contains(query) ?? false);
        }).toList();
      }
    });
  }

  Future<void> _loadModules() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Fetch modules from courses endpoint
      final coursesResponse = await _supa.getCourses(limit: 100);
      if (coursesResponse.success && coursesResponse.data != null) {
        final allModules = <ModuleResponse>[];
        final moduleSlideCounts = <String, int>{};
        final moduleCompletedCounts = <String, int>{};
        final moduleToCourseMap = <String, String>{};
        
        // Fetch details for each course to get modules
        for (final course in coursesResponse.data!) {
          final courseDetail = await _supa.getCourseDetail(course.id);
          if (courseDetail.success && courseDetail.data != null) {
            final modules = courseDetail.data!.expandedModules;
            for (final module in modules) {
              allModules.add(module);
              moduleSlideCounts[module.id] = module.slideCount;
              moduleToCourseMap[module.id] = course.id; // Track which course owns this module
              
              // Get progress for this module
              final progress = await _progressService.getModuleProgress(
                course.id,
                module.id,
              );
              moduleCompletedCounts[module.id] = progress.completedCardIds.length;
            }
          }
        }
        
        setState(() {
          _allModules = allModules;
          _filteredModules = allModules;
          _moduleSlideCounts = moduleSlideCounts;
          _moduleCompletedCounts = moduleCompletedCounts;
          _moduleToCourseMap = moduleToCourseMap;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = coursesResponse.error ?? 'Failed to load modules';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppPageScaffold(
      title: 'All Modules',
      subtitle: '${_allModules.length} modules available',
      searchBar: AppSearchBar(
        hintText: 'Search modules...',
        controller: _searchController,
      ),
      slivers: [
        if (_isLoading)
          const SliverFillRemaining(
            child: Center(child: CircularProgressIndicator()),
          )
        else if (_error != null)
          SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.warningRed,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _error!,
                    style: TextStyle(
                      fontSize: 16,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _loadModules,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.safetyOrange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final module = _filteredModules[index];
                  final slideCount = _moduleSlideCounts[module.id] ?? 0;
                  final completedCount = _moduleCompletedCounts[module.id] ?? 0;
                  final progress = slideCount > 0 ? completedCount / slideCount : 0.0;
                  
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildModuleCard(
                      context,
                      module,
                      slideCount,
                      completedCount,
                      progress,
                    ),
                  );
                },
                childCount: _filteredModules.length,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildModuleCard(
    BuildContext context,
    ModuleResponse module,
    int slideCount,
    int completedCount,
    double progress,
  ) {
    final theme = Theme.of(context);
    final isCompleted = completedCount >= slideCount && slideCount > 0;
    
    Color statusColor;
    IconData statusIcon;
    String statusText;
    
    if (completedCount == 0) {
      statusColor = AppColors.safetyOrange;
      statusIcon = Icons.play_arrow;
      statusText = 'Start';
    } else if (isCompleted) {
      statusColor = AppColors.constructionGreen;
      statusIcon = Icons.check_circle;
      statusText = 'Completed';
    } else {
      statusColor = AppColors.highVisYellow;
      statusIcon = Icons.schedule;
      statusText = '${(progress * 100).toInt()}%';
    }
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 50 / 255),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => ModuleDetailPageApi(
                courseId: _moduleToCourseMap[module.id] ?? '',
                module: module,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 30 / 255),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: statusColor.withValues(alpha: 60 / 255),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.view_module,
                      size: 28,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          module.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Wrap(
                          spacing: 12,
                          runSpacing: 4,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  statusIcon,
                                  size: 16,
                                  color: statusColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  statusText,
                                  style: TextStyle(
                                    color: statusColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              '$completedCount of $slideCount slides',
                              style: TextStyle(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.navigate_next,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
              if (completedCount > 0) ...[
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: statusColor.withValues(alpha: 30 / 255),
                  valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  borderRadius: BorderRadius.circular(8),
                  minHeight: 8,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}