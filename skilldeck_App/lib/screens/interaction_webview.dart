import 'package:flutter/material.dart';
import 'dart:io' show Platform;
import 'package:webview_flutter/webview_flutter.dart';

class InteractionWebViewPage extends StatefulWidget {
  final String title;
  final String url;
  final Color accentColor;

  const InteractionWebViewPage({
    super.key,
    required this.title,
    required this.url,
    required this.accentColor,
  });

  @override
  State<InteractionWebViewPage> createState() => _InteractionWebViewPageState();
}

class _InteractionWebViewPageState extends State<InteractionWebViewPage> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (_) {
            if (mounted) {
              setState(() => _isLoading = false);
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    // Fallback to external browser on iOS if platform views aren't available
    if (Platform.isIOS) {
      // Attempt to present inside app; if it fails at runtime, user can use external browser
    }
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        top: true,
        bottom: false,
        child: Stack(
          children: [
            Positioned.fill(
              child: WebViewWidget(controller: _controller),
            ),
            // Top-left back button overlay
            Positioned(
              top: 12,
              left: 12,
              child: _BackButtonOverlay(),
            ),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}

class _BackButtonOverlay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
        ),
        child: const Icon(Icons.close, color: Colors.white, size: 20),
      ),
    );
  }
}


