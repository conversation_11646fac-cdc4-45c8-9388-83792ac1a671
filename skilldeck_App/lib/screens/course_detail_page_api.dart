import 'package:flutter/material.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/models/user_progress.dart' as user_progress;
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/screens/module_detail_page_api.dart';

class CourseDetailPageApi extends StatefulWidget {
  final CourseResponse course;

  const CourseDetailPageApi({
    super.key,
    required this.course,
  });

  @override
  State<CourseDetailPageApi> createState() => _CourseDetailPageApiState();
}

class _CourseDetailPageApiState extends State<CourseDetailPageApi> {
  CourseResponse? _detailedCourse;
  List<String> _learningObjectives = [];
  user_progress.CourseProgress? _courseProgress;
  Map<String, user_progress.ModuleProgress> _moduleProgressMap = {};
  bool _isLoading = true;
  bool _isBookmarked = false;
  String? _error;
  late final SupabaseRsService _supa;
  late final ProgressService _progressService;

  @override
  void initState() {
    super.initState();
    _supa = SupabaseRsService();
    _progressService = ProgressService();
    _loadCourseDetails();
  }

  Future<void> _loadCourseDetails() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load course details, learning objectives, and progress in parallel
      final futures = await Future.wait([
        _supa.getCourseDetail(widget.course.id),
        _loadLearningObjectives(),
        _loadProgressData(),
      ]);
      
      final courseRes = futures[0] as ApiResponse<CourseResponse>;
      
      if (!mounted) return;
      if (courseRes.success && courseRes.data != null) {
        setState(() {
          _detailedCourse = courseRes.data!;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = courseRes.error ?? 'Failed to load course details';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _error = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadProgressData() async {
    try {
      final courseProgress = await _progressService.getCourseProgress(widget.course.id);
      final moduleProgressMap = <String, user_progress.ModuleProgress>{};
      
      // Load progress for each module
      for (final moduleId in courseProgress.moduleProgress.keys) {
        final moduleProgress = await _progressService.getModuleProgress(widget.course.id, moduleId);
        moduleProgressMap[moduleId] = moduleProgress;
      }
      
      if (mounted) {
        setState(() {
          _courseProgress = courseProgress;
          _moduleProgressMap = moduleProgressMap;
        });
      }
    } catch (e) {
      debugPrint('Error loading progress data: $e');
    }
  }

  Future<List<String>> _loadLearningObjectives() async {
    try {
      // For now, return mock data based on course
      final objectives = <String>[];
      
      switch (widget.course.title) {
        case 'Working at Night':
          objectives.addAll([
            'Understand the Effects of Night Shifts',
            'Improve Sleep and Recovery',
            'Optimize Work-Life Integration',
          ]);
          break;
        case 'Temporary Traffic Control':
          objectives.addAll([
            'Recognize the different areas of a temporary traffic control zone',
            'Explain the different types of temporary traffic control devices',
            'Describe the role of flaggers in temporary traffic control',
          ]);
          break;
        case 'Electrical Safety':
          objectives.addAll([
            'Describe voltage and amps and how they affect electricity',
            'Explain how a circuit works and how to avoid becoming part of one',
            'List at least four electrical hazards and mitigation strategies',
          ]);
          break;
      }
      
      if (mounted) {
        setState(() {
          _learningObjectives = objectives;
        });
      }
      
      return objectives;
    } catch (e) {
      debugPrint('Error loading learning objectives: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    final course = _detailedCourse ?? widget.course;
    final modules = course.expandedModules;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.redAccent),
              const SizedBox(height: 12),
              Text(_error!),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadCourseDetails,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final totalModules = modules.length;
    final totalSlides = modules.fold<int>(0, (sum, m) => sum + m.slideCount);
    final estimatedTime = (totalSlides * 2).clamp(5, 120);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // Professional compact header
          SliverAppBar(
            pinned: true,
            elevation: 0,
            backgroundColor: theme.scaffoldBackgroundColor,
            foregroundColor: theme.colorScheme.onSurface,
            title: Text(
              course.title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(_isBookmarked ? Icons.bookmark : Icons.bookmark_border),
                onPressed: _toggleBookmark,
              ),
            ],
          ),
          
          // Course description
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Text(
                course.description ?? 'Learn essential skills and safety practices for this course.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
              ),
            ),
          ),
          
          // Progress stats section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
              child: Row(
                children: [
                  _buildProgressStat(
                    context,
                    icon: Icons.check_circle,
                    value: '${_getCompletedModulesCount()}',
                    label: 'Completed',
                    color: AppColors.constructionGreen,
                  ),
                  const SizedBox(width: 32),
                  _buildProgressStat(
                    context,
                    icon: Icons.schedule,
                    value: '${_getInProgressModulesCount()}',
                    label: 'In Progress',
                    color: AppColors.highVisYellow,
                  ),
                  const SizedBox(width: 32),
                  _buildProgressStat(
                    context,
                    icon: Icons.play_circle_outline,
                    value: '${_getNotStartedModulesCount()}',
                    label: 'Not Started',
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ),
          ),
          
          // Course stats
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Text(
                '$totalModules modules • $totalSlides cards',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
          
          // Visual separator
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              height: 1,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          
          // Modules section header - clean and simple
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(24, 32, 24, 16),
              child: Row(
                children: [
                  Text(
                    'Course Modules',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.constructionGreen.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$totalModules modules',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.constructionGreen,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Clean modules list
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final module = modules[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildModuleCard(context, module, index + 1, theme, isDark),
                  );
                },
                childCount: modules.length,
              ),
            ),
          ),
          
          const SliverToBoxAdapter(child: SizedBox(height: 80)),
        ],
      ),
    );
  }

  Widget _buildStatChip(BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: isDark ? 0.15 : 0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModuleCard(BuildContext context, ModuleResponse module, int moduleNumber, ThemeData theme, bool isDark) {
    final moduleProgress = _moduleProgressMap[module.id];
    final progressPercentage = _calculateModuleProgress(module, moduleProgress);
    final isCompleted = progressPercentage >= 1.0;
    final isStarted = progressPercentage > 0.0;
    
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted 
              ? AppColors.constructionGreen.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.1 : 0.02),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => ModuleDetailPageApi(
                  courseId: (_detailedCourse ?? widget.course).id,
                  module: module,
                ),
              ),
            ).then((_) => _loadProgressData());
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Module number with progress indicator
                Stack(
                  alignment: Alignment.center,
                  children: [
                    if (isStarted)
                      SizedBox(
                        width: 44,
                        height: 44,
                        child: CircularProgressIndicator(
                          value: progressPercentage,
                          strokeWidth: 2.5,
                          backgroundColor: AppColors.constructionGreen.withValues(alpha: 0.1),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isCompleted ? AppColors.constructionGreen : AppColors.highVisYellow,
                          ),
                        ),
                      ),
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isCompleted 
                            ? AppColors.constructionGreen.withValues(alpha: 0.1)
                            : isStarted
                                ? AppColors.highVisYellow.withValues(alpha: 0.1)
                                : theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Center(
                        child: isCompleted
                            ? Icon(
                                Icons.check,
                                size: 20,
                                color: AppColors.constructionGreen,
                              )
                            : Text(
                                moduleNumber.toString(),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: isStarted 
                                      ? AppColors.highVisYellow
                                      : theme.colorScheme.onSurfaceVariant,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                
                // Module info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        module.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Wrap(
                        spacing: 12,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.slideshow_outlined,
                                size: 14,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${module.slideCount} slides',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.schedule_outlined,
                                size: 14,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${(module.slideCount * 2).clamp(2, 30)}min',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      if (isStarted) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: Container(
                                height: 4,
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.outline.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                child: FractionallySizedBox(
                                  alignment: Alignment.centerLeft,
                                  widthFactor: progressPercentage,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: isCompleted ? AppColors.constructionGreen : AppColors.highVisYellow,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 6),
                            Flexible(
                              child: Text(
                                '${(progressPercentage * 100).toInt()}%',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: isCompleted ? AppColors.constructionGreen : AppColors.highVisYellow,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 11,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Status indicator
                Icon(
                  isCompleted
                      ? Icons.check_circle
                      : isStarted
                          ? Icons.play_circle_outline
                          : Icons.arrow_forward_ios,
                  size: isCompleted || isStarted ? 20 : 16,
                  color: isCompleted
                      ? AppColors.constructionGreen
                      : isStarted
                          ? AppColors.highVisYellow
                          : theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    final theme = Theme.of(context);
    final overallProgress = _calculateOverallProgress();
    final completedModules = _getCompletedModulesCount();
    final totalModules = (_detailedCourse ?? widget.course).expandedModules.length;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.trending_up,
              size: 16,
              color: AppColors.constructionGreen,
            ),
            const SizedBox(width: 6),
            Text(
              'Your Progress',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.constructionGreen,
              ),
            ),
            const Spacer(),
            Text(
              '${(overallProgress * 100).toInt()}% Complete',
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppColors.constructionGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: overallProgress,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.constructionGreen,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
        const SizedBox(height: 6),
        Text(
          '$completedModules of $totalModules modules completed',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  double _calculateModuleProgress(ModuleResponse module, user_progress.ModuleProgress? progress) {
    if (progress == null || module.slideCount == 0) return 0.0;
    return (progress.completedCardIds.length / module.slideCount).clamp(0.0, 1.0);
  }

  double _calculateOverallProgress() {
    final modules = (_detailedCourse ?? widget.course).expandedModules;
    if (modules.isEmpty) return 0.0;
    
    double totalProgress = 0.0;
    for (final module in modules) {
      final moduleProgress = _moduleProgressMap[module.id];
      totalProgress += _calculateModuleProgress(module, moduleProgress);
    }
    
    return (totalProgress / modules.length).clamp(0.0, 1.0);
  }

  int _getCompletedModulesCount() {
    final modules = (_detailedCourse ?? widget.course).expandedModules;
    int completed = 0;
    
    for (final module in modules) {
      final moduleProgress = _moduleProgressMap[module.id];
      if (_calculateModuleProgress(module, moduleProgress) >= 1.0) {
        completed++;
      }
    }
    
    return completed;
  }

  void _toggleBookmark() {
    setState(() {
      _isBookmarked = !_isBookmarked;
    });
    
    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isBookmarked ? 'Course bookmarked' : 'Bookmark removed'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
    
    // TODO: Persist bookmark state to storage/API
  }

  Widget _buildProgressStat(BuildContext context, {
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  int _getInProgressModulesCount() {
    final modules = (_detailedCourse ?? widget.course).expandedModules;
    int inProgress = 0;
    
    for (final module in modules) {
      final moduleProgress = _moduleProgressMap[module.id];
      final progress = _calculateModuleProgress(module, moduleProgress);
      if (progress > 0.0 && progress < 1.0) {
        inProgress++;
      }
    }
    
    return inProgress;
  }

  int _getNotStartedModulesCount() {
    final modules = (_detailedCourse ?? widget.course).expandedModules;
    int notStarted = 0;
    
    for (final module in modules) {
      final moduleProgress = _moduleProgressMap[module.id];
      final progress = _calculateModuleProgress(module, moduleProgress);
      if (progress == 0.0) {
        notStarted++;
      }
    }
    
    return notStarted;
  }

  Widget _buildMiniStatChip(BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: isDark ? 0.15 : 0.08),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: color),
          const SizedBox(width: 3),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 9,
            ),
          ),
        ],
      ),
    );
  }
}