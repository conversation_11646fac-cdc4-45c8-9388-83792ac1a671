import 'package:flutter/material.dart';
import 'package:skilldeck/models/api_models.dart';
import 'package:skilldeck/services/supabase_rs_service.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
// import 'package:skilldeck/screens/module_slides_swiper.dart';
import 'package:skilldeck/screens/module_reels_viewer.dart';

class ModuleDetailPageApi extends StatefulWidget {
  final String courseId;
  final ModuleResponse module;

  const ModuleDetailPageApi({
    super.key,
    required this.courseId,
    required this.module,
  });

  @override
  State<ModuleDetailPageApi> createState() => _ModuleDetailPageApiState();
}

class _ModuleDetailPageApiState extends State<ModuleDetailPageApi> {
  ModuleResponse? _detailedModule;
  List<SlideResponse> _slides = [];
  bool _isLoading = true;
  String? _error;
  int _currentSlideIndex = 0;
  late final SupabaseRsService _supa;

  @override
  void initState() {
    super.initState();
    _supa = SupabaseRsService();
    _loadModuleDetails();
  }

  Future<void> _loadModuleDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load module with slides using Supabase
      final moduleRes = await _supa.getModuleDetail(widget.module.id);
      if (!moduleRes.success || moduleRes.data == null) {
        setState(() {
          _error = moduleRes.error ?? 'Failed to load module';
          _isLoading = false;
        });
        return;
      }

      final module = moduleRes.data!;
      // ALWAYS fetch slides via bulk endpoint to ensure images are expanded
      // The module endpoint doesn't fully expand nested relations
      List<SlideResponse> slides = [];
      
      // Get slide IDs - they could be numbers or expanded objects
      List<dynamic> slideIds = [];
      if (module.slides is List) {
        for (var slide in (module.slides as List)) {
          if (slide is Map) {
            slideIds.add(slide['id'] ?? 0);
          } else {
            slideIds.add(slide);
          }
        }
      }
      
      // Always fetch via bulk endpoint for proper image expansion
      if (slideIds.isNotEmpty) {
        final slidesRes = await _supa.getSlides(slideIds);
        if (slidesRes.success && slidesRes.data != null) {
          slides = slidesRes.data!;
        }
      }

      // Debug: confirm images are present
      debugPrint('[UI] slides loaded: ${slides.length}, first=${slides.isNotEmpty ? slides.first.id : 'n/a'} image=${slides.isNotEmpty ? (slides.first.image?.fullUrl ?? 'null') : 'n/a'}');

      setState(() {
        _detailedModule = module;
        _slides = slides;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // Navigation is handled by the swiper in the card-based UI

  @override
  Widget build(BuildContext context) {
    final module = _detailedModule ?? widget.module;
    
    // If slides are loaded, render the original card-based design
    if (!_isLoading && _error == null && _slides.isNotEmpty) {
      // In reels-style branch, show experimental viewer
      return ModuleReelsViewer(
        courseId: widget.courseId,
        module: module,
        slides: _slides,
      );
    }

    // Loading: show neutral background + skeleton (no AppBar to avoid flicker)
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark 
            ? const Color(0xFF0F172A)
            : const Color(0xFFF9FAFB),
        body: _buildLoadingSkeleton(context, module.title),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark 
          ? const Color(0xFF0F172A)
          : const Color(0xFFF9FAFB),
      appBar: AppBar(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF0A0A0A)
            : const Color(0xFF1A1A1A),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              module.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (!_isLoading && _slides.isNotEmpty)
              Text(
                'Slide ${_currentSlideIndex + 1} of ${_slides.length}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 12,
                ),
              ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorView()
              : _buildEmptyView(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.warningRed,
            ),
            const SizedBox(height: 16),
            Text(
              _error ?? 'Something went wrong',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadModuleDetails,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.safetyOrange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No slides available for this module',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingSkeleton(BuildContext context, String title) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header placeholder
            Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 18,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Progress bar placeholder
            Container(
              height: 6,
              width: double.infinity,
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            const SizedBox(height: 20),
            // Card skeleton approximating the swiper card
            Expanded(
              child: Center(
                child: Container(
                  width: size.width * 0.9,
                  height: size.height * 0.6,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHigh,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.safetyOrange),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}