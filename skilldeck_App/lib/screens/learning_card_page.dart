import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:skilldeck/models/course.dart';
import 'package:skilldeck/models/module.dart';
import 'package:skilldeck/models/learning_card.dart';
import 'package:skilldeck/models/user_progress.dart';
import 'package:skilldeck/services/progress_service.dart';
import 'package:skilldeck/core/theme/app_theme.dart';
import 'package:skilldeck/widgets/progress_tracker.dart';

class LearningCardPage extends StatefulWidget {
  final Course course;
  final Module module;

  const LearningCardPage({
    super.key,
    required this.course,
    required this.module,
  });

  @override
  State<LearningCardPage> createState() => _LearningCardPageState();
}

class _LearningCardPageState extends State<LearningCardPage>
    with TickerProviderStateMixin {
  final ProgressService _progressService = ProgressService();
  late SwiperController _swiperController;
  late AnimationController _progressController;
  late AnimationController _cardFlipController;
  
  ModuleProgress? moduleProgress;
  int currentIndex = 0;
  bool isLoading = true;
  bool showBack = false;
  Set<int> viewedCards = {};
  
  @override
  void initState() {
    super.initState();
    _swiperController = SwiperController();
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _cardFlipController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _loadProgress();
  }

  Future<void> _loadProgress() async {
    moduleProgress = await _progressService.getModuleProgress(
      widget.course.id,
      widget.module.id,
    );
    currentIndex = moduleProgress?.currentCardIndex ?? 0;
    viewedCards.add(currentIndex);
    setState(() {
      isLoading = false;
    });
    _progressController.forward();
  }

  @override
  void dispose() {
    _swiperController.dispose();
    _progressController.dispose();
    _cardFlipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingScreen();
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF0F172A),
            const Color(0xFF1E293B),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(
          child: Stack(
            children: [
              // Background pattern
              Positioned.fill(
                child: CustomPaint(
                  painter: _BackgroundPatternPainter(),
                ),
              ),
              
              // Main content
              Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildSwipeableCards(),
                  ),
                  _buildBottomSection(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF0F172A),
            const Color(0xFF1E293B),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 20 / 255),
                  shape: BoxShape.circle,
                ),
                child: CircularProgressIndicator(
                  color: AppColors.safetyOrange,
                  strokeWidth: 3,
                ),
              ).animate(onPlay: (controller) => controller.repeat())
                .shimmer(duration: 1500.ms, color: Colors.white24),
              const SizedBox(height: 24),
              Text(
                'Loading Module',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 42,
              height: 42,
              decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 15 / 255),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                color: Colors.white.withValues(alpha: 20 / 255),
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 18,
              ),
            ),
          ).animate().fadeIn(duration: 300.ms).slideX(begin: -0.2, end: 0),
          
          const Spacer(),
          
          // Clean progress indicator
          ProgressTracker(
            currentIndex: currentIndex,
            totalItems: widget.module.cards.length,
            completedCount: viewedCards.length,
            itemType: 'card',
            showPercentage: false,
          ).animate().fadeIn(duration: 300.ms),
          
          const Spacer(),
          
          // Menu button
          GestureDetector(
            onTap: _showOptionsMenu,
            child: Container(
              width: 42,
              height: 42,
              decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 15 / 255),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                color: Colors.white.withValues(alpha: 20 / 255),
                ),
              ),
              child: const Icon(
                Icons.more_vert,
                color: Colors.white,
                size: 20,
              ),
            ),
          ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.2, end: 0),
        ],
      ),
    );
  }

  Widget _buildSwipeableCards() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Swiper(
        controller: _swiperController,
        itemCount: widget.module.cards.length,
        index: currentIndex,
        onIndexChanged: _onCardChanged,
        loop: false,
        itemBuilder: (context, index) {
          final card = widget.module.cards[index];
          final isCurrentCard = index == currentIndex;
          
          return AnimatedBuilder(
            animation: _cardFlipController,
            builder: (context, child) {
              final isShowingFront = _cardFlipController.value < 0.5;
              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(_cardFlipController.value * 3.14159),
                child: isShowingFront
                    ? _buildCardFront(card, index, isCurrentCard)
                    : Transform(
                        alignment: Alignment.center,
                        transform: Matrix4.identity()..rotateY(3.14159),
                        child: _buildCardBack(card, index),
                      ),
              );
            },
          );
        },
        layout: SwiperLayout.STACK,
        itemWidth: MediaQuery.of(context).size.width - 40,
        itemHeight: MediaQuery.of(context).size.height * 0.55,
        curve: Curves.easeOutCubic,
        duration: 400,
        scale: 0.85,
        fade: 0.3,
      ),
    );
  }

  Widget _buildCardFront(LearningCard card, int index, bool isCurrentCard) {
    final cardColor = _getCardColor(card.type);
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _cardFlipController.forward();
        setState(() => showBack = true);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            colors: [
              Colors.white,
              Colors.grey.shade50,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
            color: Colors.black.withValues(alpha: 20 / 255),
              blurRadius: 20,
              offset: const Offset(0, 10),
              spreadRadius: 5,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Stack(
            children: [
              // Card type indicator
              Positioned(
                top: 20,
                right: 20,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 30 / 255),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                    color: cardColor.withValues(alpha: 50 / 255),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getCardIcon(card.type),
                        size: 14,
                        color: cardColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getCardTypeLabel(card.type),
                        style: TextStyle(
                          color: cardColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate(target: isCurrentCard ? 1 : 0)
                .fadeIn(duration: 300.ms)
                .slideY(begin: -0.2, end: 0),
              
              // Main content
              Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Icon container
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                          cardColor.withValues(alpha: 40 / 255),
                          cardColor.withValues(alpha: 20 / 255),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                        color: cardColor.withValues(alpha: 30 / 255),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        _getCardIcon(card.type),
                        size: 60,
                        color: cardColor,
                      ),
                    ).animate(target: isCurrentCard ? 1 : 0)
                      .scale(duration: 400.ms, curve: Curves.elasticOut)
                      .then()
                      .shimmer(duration: 2000.ms, color: cardColor.withValues(alpha: 60 / 255)),
                    
                    const SizedBox(height: 32),
                    
                    // Title
                    Text(
                      card.title,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1F2937),
                        height: 1.2,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ).animate(target: isCurrentCard ? 1 : 0)
                      .fadeIn(duration: 400.ms, delay: 100.ms)
                      .slideY(begin: 0.1, end: 0),
                    
                    const SizedBox(height: 16),
                    
                    // Content preview
                    Text(
                      card.content,
                      style: TextStyle(
                        fontSize: 16,
                        color: const Color(0xFF6B7280),
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                    ).animate(target: isCurrentCard ? 1 : 0)
                      .fadeIn(duration: 400.ms, delay: 200.ms)
                      .slideY(begin: 0.1, end: 0),
                    
                    const Spacer(),
                    
                    // Tap to flip hint
                    if (isCurrentCard && !viewedCards.contains(index))
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppColors.safetyOrange.withValues(alpha: 20 / 255),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.touch_app,
                              size: 16,
                              color: AppColors.safetyOrange,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Tap to see details',
                              style: TextStyle(
                                color: AppColors.safetyOrange,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ).animate(onPlay: (controller) => controller.repeat())
                        .fadeIn(duration: 600.ms)
                        .then(delay: 2000.ms)
                        .fadeOut(duration: 600.ms)
                        .then(delay: 1000.ms),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardBack(LearningCard card, int index) {
    final cardColor = _getCardColor(card.type);
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _cardFlipController.reverse();
        setState(() => showBack = false);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            colors: [
            cardColor.withValues(alpha: 255 / 255),
            cardColor.withValues(alpha: 230 / 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
            color: cardColor.withValues(alpha: 60 / 255),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: 5,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Stack(
            children: [
              // Pattern overlay
              Positioned.fill(
                child: CustomPaint(
                painter: _CardPatternPainter(color: Colors.white.withValues(alpha: 10 / 255)),
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 20 / 255),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getCardIcon(card.type),
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getCardTypeLabel(card.type),
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                card.title,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    Divider(color: Colors.white24),
                    const SizedBox(height: 24),
                    
                    // Full content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Text(
                          card.content,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            height: 1.6,
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              _cardFlipController.reverse();
                              setState(() => showBack = false);
                            },
                            icon: Icon(Icons.flip_to_front, size: 18),
                            label: Text('Flip Back'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.white,
                              side: BorderSide(color: Colors.white30),
                              padding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _canGoNext() ? _goNext : null,
                            icon: Icon(Icons.check, size: 18),
                            label: Text('Got it!'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: cardColor,
                              padding: EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
      child: Column(
        children: [
          // Module title and card counter
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  widget.module.title,
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              ProgressTracker(
                currentIndex: currentIndex,
                totalItems: widget.module.cards.length,
                completedCount: viewedCards.length,
                itemType: 'card',
                showPercentage: false,
                showCompletedCount: true,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Clean progress bar
          AnimatedBuilder(
            animation: _progressController,
            builder: (context, child) {
              return ProgressBar(
                completedCount: (viewedCards.length * _progressController.value).round(),
                totalItems: widget.module.cards.length,
                progressColor: AppColors.safetyOrange,
                height: 8,
              );
            },
          ),
          
          const SizedBox(height: 20),
          
          // Navigation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Previous button
              GestureDetector(
                onTap: _canGoPrevious() ? _goPrevious : null,
                child: Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: _canGoPrevious()
                          ? Colors.white.withValues(alpha: 20 / 255)
                          : Colors.white.withValues(alpha: 10 / 255),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _canGoPrevious()
                          ? Colors.white.withValues(alpha: 75 / 255)
                          : Colors.white.withValues(alpha: 25 / 255),
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: _canGoPrevious()
                        ? Colors.white
                        : Colors.white30,
                    size: 20,
                  ),
                ),
              ).animate().fadeIn(duration: 300.ms).scale(delay: 100.ms),
              
              const SizedBox(width: 24),
              
              // Swipe hint
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 15 / 255),
                  borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withValues(alpha: 50 / 255)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.swipe,
                      color: Colors.white70,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Swipe to navigate',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ).animate().fadeIn(duration: 400.ms),
              
              const SizedBox(width: 24),
              
              // Next button
              GestureDetector(
                onTap: _canGoNext() ? _goNext : _completeModule,
                child: Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: currentIndex == widget.module.cards.length - 1
                          ? [AppColors.constructionGreen, AppColors.constructionGreen.withValues(alpha: 200 / 255)]
                          : [AppColors.safetyOrange, AppColors.safetyOrange.withValues(alpha: 200 / 255)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: currentIndex == widget.module.cards.length - 1
                            ? AppColors.constructionGreen.withValues(alpha: 60 / 255)
                            : AppColors.safetyOrange.withValues(alpha: 60 / 255),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    currentIndex == widget.module.cards.length - 1
                        ? Icons.check
                        : Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ).animate().fadeIn(duration: 300.ms).scale(delay: 100.ms),
            ],
          ),
        ],
      ),
    );
  }

  Color _getCardColor(CardType type) {
    switch (type) {
      case CardType.quiz:
        return AppColors.warningRed;
      case CardType.interaction:
        return AppColors.safetyOrange;
      case CardType.summary:
        return AppColors.industrialBlue;
      case CardType.content:
        return AppColors.constructionGreen;
    }
  }

  IconData _getCardIcon(CardType type) {
    switch (type) {
      case CardType.quiz:
        return Icons.quiz_outlined;
      case CardType.interaction:
        return Icons.touch_app_outlined;
      case CardType.summary:
        return Icons.summarize_outlined;
      case CardType.content:
        return Icons.lightbulb_outline;
    }
  }

  String _getCardTypeLabel(CardType type) {
    switch (type) {
      case CardType.quiz:
        return 'Quiz';
      case CardType.interaction:
        return 'Interactive';
      case CardType.summary:
        return 'Summary';
      case CardType.content:
        return 'Content';
    }
  }

  bool _canGoPrevious() => currentIndex > 0;
  bool _canGoNext() => currentIndex < widget.module.cards.length - 1;

  void _goPrevious() {
    if (_canGoPrevious()) {
      HapticFeedback.lightImpact();
      _swiperController.previous();
    }
  }

  void _goNext() {
    if (_canGoNext()) {
      HapticFeedback.lightImpact();
      _markCardComplete(widget.module.cards[currentIndex].id);
      _swiperController.next();
    }
  }

  void _onCardChanged(int index) {
    if (showBack) {
      _cardFlipController.reverse();
      setState(() => showBack = false);
    }
    
    setState(() {
      currentIndex = index;
      viewedCards.add(index);
    });
    
    _progressController.forward();
    HapticFeedback.selectionClick();
    _updateCurrentCardIndex(index);
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.restart_alt, color: Colors.white),
              title: Text('Restart Module', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _swiperController.move(0);
              },
            ),
            ListTile(
              leading: Icon(Icons.grid_view, color: Colors.white),
              title: Text('Jump to Card', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showCardGrid();
              },
            ),
            ListTile(
              leading: Icon(Icons.exit_to_app, color: Colors.white),
              title: Text('Exit Module', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCardGrid() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Text(
              'Jump to Card',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: widget.module.cards.length,
                itemBuilder: (context, index) {
                  final isViewed = viewedCards.contains(index);
                  final isCurrent = index == currentIndex;
                  
                  return GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      _swiperController.move(index);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isCurrent
                            ? AppColors.safetyOrange
                            : isViewed
                        ? Colors.white.withValues(alpha: 20 / 255)
                        : Colors.white.withValues(alpha: 10 / 255),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isCurrent
                              ? AppColors.safetyOrange
                      : Colors.white.withValues(alpha: 50 / 255),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getCardIcon(widget.module.cards[index].type),
                            color: isCurrent ? Colors.white : Colors.white70,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Card ${index + 1}',
                            style: TextStyle(
                              color: isCurrent ? Colors.white : Colors.white70,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _completeModule() {
    if (currentIndex == widget.module.cards.length - 1) {
      _markCardComplete(widget.module.cards[currentIndex].id);
      _showCompletionDialog();
    }
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.constructionGreen,
          AppColors.constructionGreen.withValues(alpha: 200 / 255),
                    ],
                  ),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle_outline,
                  color: Colors.white,
                  size: 48,
                ),
              ).animate()
                .scale(duration: 400.ms, curve: Curves.elasticOut)
                .then()
                .shimmer(duration: 1000.ms),
              
              const SizedBox(height: 24),
              
              Text(
                'Module Complete!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Great job! You\'ve completed all cards in this module.',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _swiperController.move(0);
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: BorderSide(color: Colors.white30),
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text('Review Again'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.constructionGreen,
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text('Continue'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ).animate()
          .fadeIn(duration: 300.ms)
          .scale(begin: const Offset(0.8, 0.8), curve: Curves.easeOut),
      ),
    );
  }

  Future<void> _updateCurrentCardIndex(int index) async {
    await _progressService.updateCurrentCardIndex(
      courseId: widget.course.id,
      moduleId: widget.module.id,
      cardIndex: index,
    );
  }

  Future<void> _markCardComplete(String cardId) async {
    await _progressService.markCardComplete(
      courseId: widget.course.id,
      moduleId: widget.module.id,
      cardId: cardId,
    );
  }
}

// Custom painters for visual effects
class _BackgroundPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 5 / 255)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    const spacing = 30.0;
    for (double i = 0; i < size.width; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i, size.height),
        paint,
      );
    }
    for (double i = 0; i < size.height; i += spacing) {
      canvas.drawLine(
        Offset(0, i),
        Offset(size.width, i),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _CardPatternPainter extends CustomPainter {
  final Color color;
  
  _CardPatternPainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    const radius = 60.0;
    const spacing = 120.0;
    
    for (double x = -radius; x < size.width + radius; x += spacing) {
      for (double y = -radius; y < size.height + radius; y += spacing) {
        canvas.drawCircle(Offset(x, y), radius, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}