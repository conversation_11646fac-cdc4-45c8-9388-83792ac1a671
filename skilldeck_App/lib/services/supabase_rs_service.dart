import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:skilldeck/models/api_models.dart';

/// Supabase service using supabase_rs MCP for direct SQL queries
class SupabaseRsService {
  SupabaseRsService({
    String? supabaseUrl,
    String? supabaseAnonKey,
  })  : _baseUrl = (supabaseUrl ?? const String.fromEnvironment('SUPABASE_URL', defaultValue: 'https://nwquaemdrfuhafnugbgl.supabase.co')).trim(),
        _anonKey = (supabaseAnonKey ?? const String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im53cXVhZW1kcmZ1aGFmbnVnYmdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjM3NDE0MzEsImV4cCI6MjAzOTMxNzQzMX0.4_Oh_q4Rcr6UZe7K7GtA9fjjTDiRYGYTiDuaZv2i3gI')).trim(),
        _projectId = 'nwquaemdrfuhafnugbgl';

  final String _baseUrl;
  final String _anonKey;
  final String _projectId;

  Map<String, String> get _headers => {
        'apikey': _anonKey,
        'Authorization': 'Bearer $_anonKey',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };

  String get _rest => '$_baseUrl/rest/v1';
  String get _storage => '$_baseUrl/storage/v1/object/public';

  /// Constructs a Supabase Storage URL for media files
  String getMediaUrl(String? filename, {String bucket = 'Media'}) {
    if (filename == null || filename.isEmpty) return '';
    return '$_storage/$bucket/$filename';
  }

  /// Gets thumbnail URL for a course by joining with media table
  String getCourseThumbnailUrl(int? thumbnailId, String? filename) {
    if (thumbnailId == null || filename == null || filename.isEmpty) {
      // Return a default placeholder or empty string
      return '';
    }
    return getMediaUrl(filename);
  }

  Future<ApiResponse<List<CourseResponse>>> getCourses({int limit = 50}) async {
    // Use REST path with joins to reliably include thumbnails across environments
    return _getCoursesRest(limit: limit);
  }

  Future<ApiResponse<List<CourseResponse>>> _getCoursesRest({int limit = 50}) async {
    try {
      final uri = Uri.parse(
        '$_rest/courses?select=id,title,description,thumbnail_id,created_at,updated_at,courses_rels(modules_id)&order=updated_at.desc&limit=$limit',
      );
      final res = await http.get(uri, headers: _headers).timeout(const Duration(seconds: 20));
      if (res.statusCode != 200) {
        return ApiResponse(success: false, error: 'HTTP ${res.statusCode}: ${res.body}');
      }
      final List raw = jsonDecode(res.body) as List;
      // Collect thumbnail IDs
      final thumbIds = raw
          .map((row) => (row as Map<String, dynamic>)['thumbnail_id']?.toString())
          .whereType<String>()
          .where((s) => s.isNotEmpty)
          .toSet()
          .toList();

      Map<String, MediaResponse> mediaMap = {};
      if (thumbIds.isNotEmpty) {
        final ids = thumbIds.join(',');
        final mediaUri = Uri.parse(
          '$_rest/media?select=id,url,filename,mime_type,width,height,sizes_card_url,sizes_thumbnail_url&id=in.($ids)',
        );
        final mediaRes = await http.get(mediaUri, headers: _headers).timeout(const Duration(seconds: 20));
        if (mediaRes.statusCode == 200) {
          final List mlist = jsonDecode(mediaRes.body) as List;
          for (final m in mlist) {
            final mm = m as Map<String, dynamic>;
            String? imageUrl = mm['url'];
            if (imageUrl == null && mm['filename'] != null) {
              final filename = mm['filename'] as String;
              imageUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$filename';
            }
            String? cardUrl = mm['sizes_card_url'];
            String? thumbnailUrl = mm['sizes_thumbnail_url'];
            if (imageUrl != null && mm['filename'] != null) {
              final filename = mm['filename'] as String;
              final nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
              final ext = filename.substring(filename.lastIndexOf('.'));
              cardUrl ??= 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$nameWithoutExt-400x300$ext';
              thumbnailUrl ??= 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$nameWithoutExt-768x1024$ext';
            }
            mediaMap[(mm['id'] ?? '').toString()] = MediaResponse(
              id: (mm['id'] ?? '').toString(),
              url: imageUrl,
              sizes: {
                'card': {'url': cardUrl ?? imageUrl ?? ''},
                'thumbnail': {'url': thumbnailUrl ?? imageUrl ?? ''},
              },
              filename: mm['filename'],
              mimeType: mm['mime_type'],
              width: (mm['width'] is num) ? (mm['width'] as num).toInt() : null,
              height: (mm['height'] is num) ? (mm['height'] as num).toInt() : null,
            );
          }
        }
      }

      final courses = raw.map<CourseResponse>((row) {
        final map = row as Map<String, dynamic>;
        final List rels = (map['courses_rels'] as List? ?? []);
        final moduleIds = rels.map((r) => (r['modules_id'] ?? '').toString()).where((s) => s.isNotEmpty).toList();
        final thumbId = map['thumbnail_id']?.toString();
        return CourseResponse(
          id: (map['id'] ?? '').toString(),
          title: map['title'] ?? '',
          description: map['description'],
          modules: moduleIds,
          courseThumbnail: thumbId != null ? mediaMap[thumbId] : null,
          createdAt: map['created_at'] != null ? DateTime.tryParse(map['created_at']) : null,
          updatedAt: map['updated_at'] != null ? DateTime.tryParse(map['updated_at']) : null,
        );
      }).toList();
      return ApiResponse(success: true, data: courses, totalDocs: courses.length);
    } catch (e) {
      debugPrint('[SUPA_RS] getCourses error: $e');
      return ApiResponse(success: false, error: 'Network error: $e');
    }
  }

  Future<ApiResponse<CourseResponse>> getCourseDetail(String courseId) async {
    try {
      // Complex query with nested joins
      final query = '''
        SELECT 
          c.id,
          c.title,
          c.description,
          c.created_at,
          c.updated_at,
          COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'order', cr.order,
                'modules', jsonb_build_object(
                  'id', m.id,
                  'title', m.title,
                  'description', m.description,
                  'module_thumbnail_id', m.module_thumbnail_id,
                  'created_at', m.created_at,
                  'updated_at', m.updated_at,
                  'modules_rels', (
                    SELECT COALESCE(
                      json_agg(
                        jsonb_build_object(
                          'order', mr.order,
                          'slides', jsonb_build_object('id', s.id)
                        ) ORDER BY mr.order
                      ),
                      '[]'::json
                    )
                    FROM modules_rels mr
                    LEFT JOIN slides s ON mr.slides_id = s.id
                    WHERE mr.parent_id = m.id
                  )
                )
              ) ORDER BY cr.order
            ) FILTER (WHERE m.id IS NOT NULL),
            '[]'::json
          ) as courses_rels
        FROM courses c
        LEFT JOIN courses_rels cr ON c.id = cr.parent_id
        LEFT JOIN modules m ON cr.modules_id = m.id
        WHERE c.id = $courseId
        GROUP BY c.id
      ''';

      final uri = Uri.parse('$_rest/rpc/query');
      final res = await http.post(
        uri,
        headers: _headers,
        body: jsonEncode({'query': query}),
      ).timeout(const Duration(seconds: 20));

      if (res.statusCode != 200) {
        // Fallback to REST API
        return _getCourseDetailRest(courseId);
      }

      final List list = jsonDecode(res.body) as List;
      if (list.isEmpty) return ApiResponse(success: false, error: 'Not found');
      final row = list.first as Map<String, dynamic>;

      final modulesRels = (row['courses_rels'] as List? ?? []);
      final expandedModules = modulesRels.map<ModuleResponse>((rel) {
        final m = (rel['modules'] ?? {}) as Map<String, dynamic>;
        final slidesRels = (m['modules_rels'] as List? ?? []);
        final slideIds = slidesRels.map((sr) => (sr['slides']?['id'] ?? '').toString()).where((s) => s.isNotEmpty).toList();
        return ModuleResponse(
          id: (m['id'] ?? '').toString(),
          title: m['title'] ?? '',
          description: m['description'],
          slides: slideIds,
          moduleThumbnail: null,
          createdAt: m['created_at'] != null ? DateTime.tryParse(m['created_at']) : null,
          updatedAt: m['updated_at'] != null ? DateTime.tryParse(m['updated_at']) : null,
        );
      }).toList();

      final course = CourseResponse(
        id: (row['id'] ?? '').toString(),
        title: row['title'] ?? '',
        description: row['description'],
        modules: expandedModules.map((m) => {
          'id': m.id,
          'title': m.title,
          'description': m.description,
          'slides': m.slideIds,
        }).toList(),
        courseThumbnail: null,
        createdAt: row['created_at'] != null ? DateTime.tryParse(row['created_at']) : null,
        updatedAt: row['updated_at'] != null ? DateTime.tryParse(row['updated_at']) : null,
      );
      return ApiResponse(success: true, data: course);
    } catch (e) {
      // Fallback to REST API
      return _getCourseDetailRest(courseId);
    }
  }

  Future<ApiResponse<CourseResponse>> _getCourseDetailRest(String courseId) async {
    try {
      final uri = Uri.parse(
        '$_rest/courses?select=id,title,description,created_at,updated_at,courses_rels(order,modules:modules(id,title,description,module_thumbnail_id,created_at,updated_at,modules_rels(order,slides:slides(id))))&id=eq.$courseId',
      );
      final res = await http.get(uri, headers: _headers).timeout(const Duration(seconds: 20));
      if (res.statusCode != 200) {
        return ApiResponse(success: false, error: 'HTTP ${res.statusCode}: ${res.body}');
      }
      final List list = jsonDecode(res.body) as List;
      if (list.isEmpty) return ApiResponse(success: false, error: 'Not found');
      final row = list.first as Map<String, dynamic>;

      final modulesRels = (row['courses_rels'] as List? ?? []);
      final expandedModules = modulesRels.map<ModuleResponse>((rel) {
        final m = (rel['modules'] ?? {}) as Map<String, dynamic>;
        final slidesRels = (m['modules_rels'] as List? ?? []);
        final slideIds = slidesRels.map((sr) => (sr['slides']?['id'] ?? '').toString()).where((s) => s.isNotEmpty).toList();
        return ModuleResponse(
          id: (m['id'] ?? '').toString(),
          title: m['title'] ?? '',
          description: m['description'],
          slides: slideIds,
          moduleThumbnail: null,
          createdAt: m['created_at'] != null ? DateTime.tryParse(m['created_at']) : null,
          updatedAt: m['updated_at'] != null ? DateTime.tryParse(m['updated_at']) : null,
        );
      }).toList();

      final course = CourseResponse(
        id: (row['id'] ?? '').toString(),
        title: row['title'] ?? '',
        description: row['description'],
        modules: expandedModules.map((m) => {
          'id': m.id,
          'title': m.title,
          'description': m.description,
          'slides': m.slideIds,
        }).toList(),
        courseThumbnail: null,
        createdAt: row['created_at'] != null ? DateTime.tryParse(row['created_at']) : null,
        updatedAt: row['updated_at'] != null ? DateTime.tryParse(row['updated_at']) : null,
      );
      return ApiResponse(success: true, data: course);
    } catch (e) {
      debugPrint('[SUPA_RS] getCourseDetail error: $e');
      return ApiResponse(success: false, error: 'Network error: $e');
    }
  }

  Future<ApiResponse<ModuleResponse>> getModuleDetail(String moduleId) async {
    try {
      final query = '''
        SELECT 
          m.id,
          m.title,
          m.description,
          m.created_at,
          m.updated_at,
          COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'order', mr.order,
                'slides', jsonb_build_object('id', s.id)
              ) ORDER BY mr.order
            ) FILTER (WHERE s.id IS NOT NULL),
            '[]'::json
          ) as modules_rels
        FROM modules m
        LEFT JOIN modules_rels mr ON m.id = mr.parent_id
        LEFT JOIN slides s ON mr.slides_id = s.id
        WHERE m.id = $moduleId
        GROUP BY m.id
      ''';

      final uri = Uri.parse('$_rest/rpc/query');
      final res = await http.post(
        uri,
        headers: _headers,
        body: jsonEncode({'query': query}),
      ).timeout(const Duration(seconds: 20));

      if (res.statusCode != 200) {
        // Fallback to REST API
        return _getModuleDetailRest(moduleId);
      }

      final List list = jsonDecode(res.body) as List;
      if (list.isEmpty) return ApiResponse(success: false, error: 'Not found');
      final row = list.first as Map<String, dynamic>;
      final rels = (row['modules_rels'] as List? ?? []);
      final slideIds = rels.map((r) => (r['slides']?['id'] ?? '').toString()).where((s) => s.isNotEmpty).toList();
      final module = ModuleResponse(
        id: (row['id'] ?? '').toString(),
        title: row['title'] ?? '',
        description: row['description'],
        slides: slideIds,
        moduleThumbnail: null,
        createdAt: row['created_at'] != null ? DateTime.tryParse(row['created_at']) : null,
        updatedAt: row['updated_at'] != null ? DateTime.tryParse(row['updated_at']) : null,
      );
      return ApiResponse(success: true, data: module);
    } catch (e) {
      // Fallback to REST API
      return _getModuleDetailRest(moduleId);
    }
  }

  Future<ApiResponse<ModuleResponse>> _getModuleDetailRest(String moduleId) async {
    try {
      final uri = Uri.parse(
        '$_rest/modules?id=eq.$moduleId&select=id,title,description,created_at,updated_at,modules_rels(order,slides:slides(id))',
      );
      final res = await http.get(uri, headers: _headers).timeout(const Duration(seconds: 20));
      if (res.statusCode != 200) {
        return ApiResponse(success: false, error: 'HTTP ${res.statusCode}: ${res.body}');
      }
      final List list = jsonDecode(res.body) as List;
      if (list.isEmpty) return ApiResponse(success: false, error: 'Not found');
      final row = list.first as Map<String, dynamic>;
      final rels = (row['modules_rels'] as List? ?? []);
      final slideIds = rels.map((r) => (r['slides']?['id'] ?? '').toString()).where((s) => s.isNotEmpty).toList();
      final module = ModuleResponse(
        id: (row['id'] ?? '').toString(),
        title: row['title'] ?? '',
        description: row['description'],
        slides: slideIds,
        moduleThumbnail: null,
        createdAt: row['created_at'] != null ? DateTime.tryParse(row['created_at']) : null,
        updatedAt: row['updated_at'] != null ? DateTime.tryParse(row['updated_at']) : null,
      );
      return ApiResponse(success: true, data: module);
    } catch (e) {
      debugPrint('[SUPA_RS] getModuleDetail error: $e');
      return ApiResponse(success: false, error: 'Network error: $e');
    }
  }

  Future<ApiResponse<List<SlideResponse>>> getSlides(List<dynamic> slideIds) async {
    if (slideIds.isEmpty) return ApiResponse(success: true, data: []);
    try {
      final ids = slideIds.map((e) => e.toString()).join(',');
      final query = '''
        SELECT 
          s.id,
          s.title,
          s.description,
          s.type,
          s.created_at,
          s.updated_at,
          jsonb_build_object(
            'id', m.id,
            'url', m.url,
            'filename', m.filename,
            'mime_type', m.mime_type,
            'width', m.width,
            'height', m.height,
            'sizes_card_url', m.sizes_card_url,
            'sizes_thumbnail_url', m.sizes_thumbnail_url
          ) as image
        FROM slides s
        LEFT JOIN media m ON s.image_id = m.id
        WHERE s.id IN ($ids)
      ''';

      final uri = Uri.parse('$_rest/rpc/query');
      final res = await http.post(
        uri,
        headers: _headers,
        body: jsonEncode({'query': query}),
      ).timeout(const Duration(seconds: 20));

      if (res.statusCode != 200) {
        // Fallback to REST API
        return _getSlidesRest(slideIds);
      }

      final List list = jsonDecode(res.body) as List;
      final slides = list.map<SlideResponse>((row) {
        final img = row['image'] as Map<String, dynamic>?;
        MediaResponse? media;
        if (img != null && img['id'] != null) {
          // Construct URL from filename if URL is null
          String? imageUrl = img['url'];
          if (imageUrl == null && img['filename'] != null) {
            // Use Supabase Storage public URL
            final filename = img['filename'] as String;
            // Construct Supabase Storage URL - files are stored with media/ prefix
            imageUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$filename';
          }
          
          // Construct size variants
          String? cardUrl = img['sizes_card_url'];
          String? thumbnailUrl = img['sizes_thumbnail_url'];
          
          if (imageUrl != null && img['filename'] != null) {
            final filename = img['filename'] as String;
            final nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
            final ext = filename.substring(filename.lastIndexOf('.'));
            
            // If no card URL, construct it
            if (cardUrl == null) {
              cardUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$nameWithoutExt-400x300$ext';
            }
            
            // If no thumbnail URL, use the 768x1024 version
            if (thumbnailUrl == null) {
              thumbnailUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$nameWithoutExt-768x1024$ext';
            }
          }
          
          media = MediaResponse(
            id: (img['id'] ?? '').toString(),
            url: imageUrl,
            sizes: {
              'card': {
                'url': cardUrl ?? imageUrl ?? '',
              },
              'thumbnail': {
                'url': thumbnailUrl ?? imageUrl ?? '',
              },
            },
            filename: img['filename'],
            mimeType: img['mime_type'],
            width: (img['width'] is num) ? (img['width'] as num).toInt() : null,
            height: (img['height'] is num) ? (img['height'] as num).toInt() : null,
          );
        }
        return SlideResponse(
          id: (row['id'] ?? '').toString(),
          title: row['title'] ?? '',
          content: row['description'],
          type: (row['type'] ?? 'content').toString(),
          image: media,
          quizData: null,
          createdAt: row['created_at'] != null ? DateTime.tryParse(row['created_at']) : null,
          updatedAt: row['updated_at'] != null ? DateTime.tryParse(row['updated_at']) : null,
        );
      }).toList();
      return ApiResponse(success: true, data: slides);
    } catch (e) {
      // Fallback to REST API
      return _getSlidesRest(slideIds);
    }
  }

  Future<ApiResponse<List<SlideResponse>>> _getSlidesRest(List<dynamic> slideIds) async {
    if (slideIds.isEmpty) return ApiResponse(success: true, data: []);
    try {
      final ids = slideIds.map((e) => e.toString()).join(',');
      final uri = Uri.parse(
        '$_rest/slides?select=id,title,description,type,created_at,updated_at,image:media!slides_image_id_media_id_fk(id,url,filename,mime_type,width,height,sizes_card_url,sizes_thumbnail_url)&id=in.($ids)',
      );
      final res = await http.get(uri, headers: _headers).timeout(const Duration(seconds: 20));
      if (res.statusCode != 200) {
        return ApiResponse(success: false, error: 'HTTP ${res.statusCode}: ${res.body}');
      }
      final List list = jsonDecode(res.body) as List;
      final slides = list.map<SlideResponse>((row) {
        final img = row['image'] as Map<String, dynamic>?;
        MediaResponse? media;
        if (img != null) {
          // Construct URL from filename if URL is null
          String? imageUrl = img['url'];
          if (imageUrl == null && img['filename'] != null) {
            // Use Supabase Storage public URL
            final filename = img['filename'] as String;
            // Construct Supabase Storage URL - files are stored with media/ prefix
            imageUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$filename';
          }
          
          // Construct size variants
          String? cardUrl = img['sizes_card_url'];
          String? thumbnailUrl = img['sizes_thumbnail_url'];
          
          if (imageUrl != null && img['filename'] != null) {
            final filename = img['filename'] as String;
            final nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
            final ext = filename.substring(filename.lastIndexOf('.'));
            
            // If no card URL, construct it
            if (cardUrl == null) {
              cardUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$nameWithoutExt-400x300$ext';
            }
            
            // If no thumbnail URL, use the 768x1024 version
            if (thumbnailUrl == null) {
              thumbnailUrl = 'https://$_projectId.supabase.co/storage/v1/object/public/Media/media/$nameWithoutExt-768x1024$ext';
            }
          }
          
          media = MediaResponse(
            id: (img['id'] ?? '').toString(),
            url: imageUrl,
            sizes: {
              'card': {
                'url': cardUrl ?? imageUrl ?? '',
              },
              'thumbnail': {
                'url': thumbnailUrl ?? imageUrl ?? '',
              },
            },
            filename: img['filename'],
            mimeType: img['mime_type'],
            width: (img['width'] is num) ? (img['width'] as num).toInt() : null,
            height: (img['height'] is num) ? (img['height'] as num).toInt() : null,
          );
        }
        return SlideResponse(
          id: (row['id'] ?? '').toString(),
          title: row['title'] ?? '',
          content: row['description'],
          type: (row['type'] ?? 'content').toString(),
          image: media,
          quizData: null,
          createdAt: row['created_at'] != null ? DateTime.tryParse(row['created_at']) : null,
          updatedAt: row['updated_at'] != null ? DateTime.tryParse(row['updated_at']) : null,
        );
      }).toList();
      return ApiResponse(success: true, data: slides);
    } catch (e) {
      debugPrint('[SUPA_RS] getSlides error: $e');
      return ApiResponse(success: false, error: 'Network error: $e');
    }
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final int? totalDocs;
  ApiResponse({required this.success, this.data, this.error, this.totalDocs});
}