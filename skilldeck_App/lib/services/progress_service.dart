import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skilldeck/models/user_progress.dart';

class ProgressService {
  static final ProgressService _instance = ProgressService._internal();
  factory ProgressService() => _instance;
  ProgressService._internal();

  static const String _userProgressKey = 'user_progress';
  static const String _defaultUserId = 'default_user';

  Future<UserProgress> getUserProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString(_userProgressKey);
    
    if (progressJson != null) {
      return UserProgress.fromJson(jsonDecode(progressJson));
    }
    
    return UserProgress(
      userId: _defaultUserId,
      courseProgress: {},
    );
  }

  Future<void> saveUserProgress(UserProgress progress) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userProgressKey, jsonEncode(progress.toJson()));
  }

  Future<CourseProgress> getCourseProgress(String courseId) async {
    final userProgress = await getUserProgress();
    return userProgress.courseProgress[courseId] ?? CourseProgress(
      courseId: courseId,
      moduleProgress: {},
    );
  }

  Future<void> updateCourseProgress(CourseProgress courseProgress) async {
    final userProgress = await getUserProgress();
    final updatedProgress = UserProgress(
      userId: userProgress.userId,
      courseProgress: {
        ...userProgress.courseProgress,
        courseProgress.courseId: courseProgress,
      },
    );
    await saveUserProgress(updatedProgress);
  }

  Future<ModuleProgress> getModuleProgress(String courseId, String moduleId) async {
    final courseProgress = await getCourseProgress(courseId);
    return courseProgress.moduleProgress[moduleId] ?? ModuleProgress(
      moduleId: moduleId,
      completedCardIds: {},
    );
  }

  Future<void> updateModuleProgress({
    required String courseId,
    required String moduleId,
    required ModuleProgress moduleProgress,
  }) async {
    final courseProgress = await getCourseProgress(courseId);
    final updatedCourseProgress = CourseProgress(
      courseId: courseProgress.courseId,
      moduleProgress: {
        ...courseProgress.moduleProgress,
        moduleId: moduleProgress,
      },
      lastAccessed: DateTime.now(),
    );
    await updateCourseProgress(updatedCourseProgress);
  }

  Future<void> markCardComplete({
    required String courseId,
    required String moduleId,
    required String cardId,
  }) async {
    final moduleProgress = await getModuleProgress(courseId, moduleId);
    final updatedModuleProgress = ModuleProgress(
      moduleId: moduleProgress.moduleId,
      completedCardIds: {...moduleProgress.completedCardIds, cardId},
      currentCardIndex: moduleProgress.currentCardIndex,
      lastAccessed: DateTime.now(),
    );
    await updateModuleProgress(
      courseId: courseId,
      moduleId: moduleId,
      moduleProgress: updatedModuleProgress,
    );
  }

  Future<void> markSlideComplete({
    required String courseId,
    required String moduleId,
    required String slideId,
  }) async {
    await markCardComplete(
      courseId: courseId,
      moduleId: moduleId,
      cardId: slideId,
    );
  }

  Future<double> getModuleCompletionPercentage({
    required String courseId,
    required String moduleId,
    required int totalSlides,
  }) async {
    final moduleProgress = await getModuleProgress(courseId, moduleId);
    if (totalSlides == 0) return 0.0;
    return (moduleProgress.completedCardIds.length / totalSlides).clamp(0.0, 1.0);
  }

  Future<void> updateCurrentCardIndex({
    required String courseId,
    required String moduleId,
    required int cardIndex,
  }) async {
    final moduleProgress = await getModuleProgress(courseId, moduleId);
    final updatedModuleProgress = ModuleProgress(
      moduleId: moduleProgress.moduleId,
      completedCardIds: moduleProgress.completedCardIds,
      currentCardIndex: cardIndex,
      lastAccessed: DateTime.now(),
    );
    await updateModuleProgress(
      courseId: courseId,
      moduleId: moduleId,
      moduleProgress: updatedModuleProgress,
    );
  }

  Future<double> getCourseCompletionPercentage(String courseId) async {
    final courseProgress = await getCourseProgress(courseId);
    return courseProgress.completionPercentage;
  }

  Future<int> getCompletedModulesCount(String courseId) async {
    final courseProgress = await getCourseProgress(courseId);
    return courseProgress.completedModules;
  }
}