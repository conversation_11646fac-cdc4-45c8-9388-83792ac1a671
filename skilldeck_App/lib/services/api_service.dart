import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../core/config/api_config.dart';
import '../models/api_models.dart';

class ApiService {
  // Reads JWT from secure storage and adds it to requests when available
  ApiService();

  static const _tokenKey = 'jwt_token';
  static const _defaultTimeout = Duration(seconds: 20);
  static const _storage = FlutterSecureStorage();

  Future<String?> _ensureAuthToken() async {
    try {
      final existing = await _storage.read(key: _tokenKey);
      if (existing != null && existing.isNotEmpty) return existing;
      // Silent login using dev credentials so endpoints are authorized
      final res = await http.post(
        Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.loginEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': ApiConfig.testEmail,
          'password': ApiConfig.testPassword,
        }),
      );
      if (res.statusCode == 200) {
        final body = jsonDecode(res.body);
        final token = body['token'] as String?;
        if (token != null) {
          await _storage.write(key: _tokenKey, value: token);
          return token;
        }
      }
    } catch (_) {}
    return null;
  }

  Future<Map<String, String>?> _maybeAuthHeaders() async {
    try {
      final token = await _ensureAuthToken();
      if (token == null || token.isEmpty) return null;
      return {
        'Authorization': 'JWT $token',
      };
    } catch (_) {
      return null;
    }
  }
  
  Future<ApiResponse<List<CourseResponse>>> getCourses({
    int limit = 50,
    int depth = 0,
  }) async {
    try {
      final response = await http
          .get(
            Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.coursesEndpoint}?limit=$limit&depth=$depth'),
            headers: await _maybeAuthHeaders(),
          )
          .timeout(_defaultTimeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final courses = (data['docs'] as List)
            .map((json) => CourseResponse.fromJson(json))
            .toList();
        
        return ApiResponse(
          success: true,
          data: courses,
          totalDocs: data['totalDocs'],
          totalPages: data['totalPages'],
          page: data['page'],
        );
      } else if (response.statusCode == 401) {
        return ApiResponse(
          success: false,
          error: 'Access denied. Please check your connection.',
        );
      } else {
        return ApiResponse(
          success: false,
          error: 'Failed to load courses',
        );
      }
    } catch (e) {
      debugPrint('Error fetching courses: $e');
      return ApiResponse(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }
  
  Future<ApiResponse<CourseResponse>> getCourseDetail(String courseId, {int depth = 1}) async {
    try {
      final url = '${ApiConfig.apiBaseUrl}${ApiConfig.coursesEndpoint}/$courseId?depth=$depth';
      final response = await http
          .get(
            Uri.parse(url),
            headers: await _maybeAuthHeaders(),
          )
          .timeout(_defaultTimeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return ApiResponse(
          success: true,
          data: CourseResponse.fromJson(data),
        );
      } else if (response.statusCode == 401) {
        debugPrint('[API] 401 on GET $url body=${response.body}');
        return ApiResponse(
          success: false,
          error: 'Access denied. Please check your connection.',
        );
      } else {
        debugPrint('[API] GET $url failed status=${response.statusCode} body=${response.body}');
        return ApiResponse(
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('[API] Exception GET course detail: $e');
      return ApiResponse(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }
  
  Future<ApiResponse<ModuleResponse>> getModuleDetail(String moduleId, {int depth = 1}) async {
    try {
      final url = '${ApiConfig.apiBaseUrl}${ApiConfig.modulesEndpoint}/$moduleId?depth=$depth';
      final response = await http
          .get(
            Uri.parse(url),
            headers: await _maybeAuthHeaders(),
          )
          .timeout(_defaultTimeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return ApiResponse(
          success: true,
          data: ModuleResponse.fromJson(data),
        );
      } else if (response.statusCode == 401) {
        debugPrint('[API] 401 on GET $url body=${response.body}');
        return ApiResponse(
          success: false,
          error: 'Access denied. Please check your connection.',
        );
      } else {
        debugPrint('[API] GET $url failed status=${response.statusCode} body=${response.body}');
        return ApiResponse(
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('[API] Exception GET module detail: $e');
      return ApiResponse(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }
  
  Future<ApiResponse<List<SlideResponse>>> getSlides(List<dynamic> slideIds, {int depth = 1}) async {
    if (slideIds.isEmpty) {
      return ApiResponse(success: true, data: []);
    }
    
    try {
      // Ensure all IDs are strings to avoid type errors when joining
      final idsParam = slideIds.map((e) => e.toString()).join(',');
      final url = '${ApiConfig.apiBaseUrl}${ApiConfig.slidesEndpoint}?where[id][in]=$idsParam&limit=200&depth=$depth';
      final response = await http
          .get(
            Uri.parse(url),
            headers: await _maybeAuthHeaders(),
          )
          .timeout(_defaultTimeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final slides = <SlideResponse>[];
        for (var slideJson in (data['docs'] as List)) {
          try {
            slides.add(SlideResponse.fromJson(slideJson));
          } catch (e, stack) {
            debugPrint('[API] Error parsing slide: $e');
            debugPrint('[API] Slide JSON: $slideJson');
            debugPrint('[API] Stack: $stack');
          }
        }
        
        return ApiResponse(
          success: true,
          data: slides,
        );
      } else if (response.statusCode == 401) {
        debugPrint('[API] 401 on GET $url body=${response.body}');
        return ApiResponse(
          success: false,
          error: 'Access denied. Please check your connection.',
        );
      } else {
        debugPrint('[API] GET $url failed status=${response.statusCode} body=${response.body}');
        return ApiResponse(
          success: false,
          error: 'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('[API] Exception GET slides: $e');
      return ApiResponse(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }
  
  // Progress endpoints - to be implemented when server endpoints are available
  Future<ModuleProgress?> getModuleProgress(String moduleId) async {
    try {
      final response = await http
          .get(
            Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.progressModuleEndpoint}/$moduleId'),
            headers: await _maybeAuthHeaders(),
          )
          .timeout(_defaultTimeout);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return ModuleProgress.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('Progress endpoint not available yet: $e');
      return null;
    }
  }
  
  Future<bool> markSlideComplete(String moduleId, String slideId) async {
    try {
      final headers = (await _maybeAuthHeaders()) ?? {};
      headers['Content-Type'] = 'application/json';
      final response = await http
          .post(
            Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.progressSlideEndpoint}'),
            headers: headers,
            body: jsonEncode({
              'moduleId': moduleId,
              'slideId': slideId,
              'completed': true,
            }),
          )
          .timeout(_defaultTimeout);
      
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Progress endpoint not available yet: $e');
      return false;
    }
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final int? totalDocs;
  final int? totalPages;
  final int? page;
  
  ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.totalDocs,
    this.totalPages,
    this.page,
  });
}