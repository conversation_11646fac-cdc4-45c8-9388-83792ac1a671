import 'package:skilldeck/models/course.dart';
import 'package:skilldeck/models/module.dart';
import 'package:skilldeck/models/learning_card.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  List<Course> getCourses() => [
    Course(
      id: 'working_night',
      title: 'Working at Night',
      description: 'This course explores the challenges and benefits of working night shifts, covering topics like productivity, health impacts, sleep management, and work-life balance.',
      imageUrl: 'https://pixabay.com/get/g555f39fe1feda60d867679ce4bb8ef24ae98e6f19129f227db417867866cee9de9d078d815bdf717ca482cb7cea4867fd3b576918403fe3485188b97cd11a865_1280.jpg',
      modules: [
        Module(
          id: 'worker_fatigue',
          title: 'Worker Fatigue',
          description: 'Learn about managing fatigue during night shifts',
          iconName: 'bedtime',
          cards: [
            LearningCard(
              id: 'fatigue_1',
              title: 'Healthful Eating While Working at Night',
              content: 'Maintaining proper nutrition is crucial for night shift workers to sustain energy levels and overall health.',
              imageUrl: 'https://pixabay.com/get/gf9de9a4affdaf798b8c56a688c2369194c296a32ab57393dbecec52b512c554a83a14d3b9bfc642a3ed48827a897e57d52cd77cfbad0e5f92e3e6e388947ff50_1280.jpg',
              bulletPoints: [
                'Eat your main meal before work',
                'Pack healthful snacks such as low-fat cheese, nuts, and fruit',
                'Avoid sugary foods and drinks',
                'Drink plenty of water',
                'Minimize caffeine intake to ensure you sleep during the day',
                'Avoid alcohol after work because it can disturb your sleep',
                'Have a light meal before going to bed such as a bowl of whole grain cereal with milk or toast with peanut butter'
              ],
              backContent: 'Proper nutrition helps maintain alertness and prevents the energy crashes common during night shifts.',
            ),
            LearningCard(
              id: 'fatigue_2',
              title: 'Recognizing Fatigue Signs',
              content: 'Night shift workers must be able to identify when fatigue is affecting their performance and safety.',
              bulletPoints: [
                'Heavy eyelids and frequent blinking',
                'Difficulty concentrating on tasks',
                'Slower reaction times',
                'Increased errors or near-misses',
                'Mood changes and irritability',
                'Physical symptoms like headaches'
              ],
              backContent: 'Early recognition of fatigue signs allows workers to take appropriate countermeasures before safety is compromised.',
            ),
          ],
        ),
        Module(
          id: 'night_hazards',
          title: 'The Hazards of Working at Night',
          description: 'Understanding unique risks of nighttime work',
          iconName: 'warning',
          cards: [
            LearningCard(
              id: 'hazards_1',
              title: 'Reduced Visibility Risks',
              content: 'Limited visibility during night work creates additional safety challenges that must be addressed.',
              imageUrl: 'https://pixabay.com/get/g6a7b435a7843340c500ffddf6515f2926ab0ca0b7a828b1ee61d8279f3daacaff91a78a830230d8c12415bcf0d13bc25c29a57ebf1f21a587c37fd6a1226c3b3_1280.jpg',
              bulletPoints: [
                'Use additional lighting equipment',
                'Wear high-visibility reflective clothing',
                'Move more slowly and deliberately',
                'Increase communication with team members',
                'Conduct more frequent safety checks'
              ],
            ),
          ],
        ),
        Module(
          id: 'lighting_night',
          title: 'Lighting for Night Work',
          description: 'Proper illumination strategies for safe night operations',
          iconName: 'lightbulb',
          cards: [
            LearningCard(
              id: 'lighting_1',
              title: 'Essential Lighting Requirements',
              content: 'Adequate lighting is critical for maintaining safety and productivity during night work operations.',
              imageUrl: 'https://pixabay.com/get/g212e28a567621884433b0d7f984466bcb0b6f09978985351a6d44b3eb841fefa33d24714ad62ece7c5e26ad8553a3010281938ae7bf38a062012ac409af82815_1280.jpg',
              bulletPoints: [
                'Minimum 50 lux for general work areas',
                'Higher intensity for detailed tasks',
                'Even distribution to avoid shadows',
                'Portable lights for mobile operations',
                'Emergency backup lighting systems'
              ],
            ),
          ],
        ),
      ],
    ),
    Course(
      id: 'traffic_control',
      title: 'Temporary Traffic Control',
      description: 'Learn about safe traffic management in work zones with proper signage, equipment, and procedures.',
      imageUrl: 'https://pixabay.com/get/gcd20b7310397ee01054c4b6aa3b1aef4ccf875dfc569f4f9882923e469a8c1c446346d0d470c63eafe8ca34e3bec86bdc4aeb33f3f55dd7c3dfbacdda057fe54_1280.jpg',
      modules: [
        Module(
          id: 'traffic_signs',
          title: 'Traffic Control Devices',
          description: 'Understanding signs, cones, and barriers',
          iconName: 'traffic',
          cards: [
            LearningCard(
              id: 'signs_1',
              title: 'Work Zone Warning Signs',
              content: 'Proper placement and selection of warning signs is essential for motorist safety and work zone effectiveness.',
              imageUrl: 'https://pixabay.com/get/g37bb36d3d3eb9bb6c974107fd94dd85fceef0f9e9b7e7b89ee23c64a4dd9e74c64a86b4e322a15518c71d2ae30e9a8c2478be210cac893a2465d6ea0dfde35c6_1280.jpg',
              bulletPoints: [
                'Place signs at appropriate distances',
                'Use retroreflective materials for visibility',
                'Ensure signs are clean and readable',
                'Remove or cover irrelevant signs',
                'Follow MUTCD guidelines for placement'
              ],
            ),
          ],
        ),
        Module(
          id: 'flagging',
          title: 'Flagging Procedures',
          description: 'Safe flagging techniques and communication',
          iconName: 'flag',
          cards: [
            LearningCard(
              id: 'flagging_1',
              title: 'Basic Flagging Signals',
              content: 'Flaggers use standardized hand signals and equipment to safely direct traffic through work zones.',
              bulletPoints: [
                'STOP paddle held stationary above head',
                'SLOW paddle with free hand motion',
                'Maintain eye contact with drivers',
                'Position yourself safely with escape route',
                'Use appropriate PPE at all times'
              ],
              backContent: 'Consistent and clear signaling prevents confusion and maintains traffic flow safety.',
            ),
          ],
        ),
      ],
    ),
    Course(
      id: 'electrical_safety',
      title: 'Electrical Safety',
      description: 'Essential electrical safety practices for work zone environments and equipment operation.',
      imageUrl: 'https://pixabay.com/get/g212e28a567621884433b0d7f984466bcb0b6f09978985351a6d44b3eb841fefa33d24714ad62ece7c5e26ad8553a3010281938ae7bf38a062012ac409af82815_1280.jpg',
      modules: [
        Module(
          id: 'electrical_basics',
          title: 'Electrical Hazard Recognition',
          description: 'Identifying and avoiding electrical dangers',
          iconName: 'electrical_services',
          cards: [
            LearningCard(
              id: 'electrical_1',
              title: 'Power Line Safety',
              content: 'Working near overhead power lines requires special precautions and awareness of minimum clearance distances.',
              imageUrl: 'https://pixabay.com/get/g6a7b435a7843340c500ffddf6515f2926ab0ca0b7a828b1ee61d8279f3daacaff91a78a830230d8c12415bcf0d13bc25c29a57ebf1f21a587c37fd6a1226c3b3_1280.jpg',
              bulletPoints: [
                'Maintain minimum 10-foot clearance',
                'Use spotters when operating tall equipment',
                'Contact utility companies before work',
                'Never touch downed power lines',
                'Use non-conductive tools when possible'
              ],
            ),
          ],
        ),
      ],
    ),
  ];

  Course? getCourseById(String id) {
    try {
      return getCourses().firstWhere((course) => course.id == id);
    } catch (e) {
      return null;
    }
  }

  Module? getModuleById(String courseId, String moduleId) {
    final course = getCourseById(courseId);
    try {
      return course?.modules.firstWhere((module) => module.id == moduleId);
    } catch (e) {
      return null;
    }
  }
}