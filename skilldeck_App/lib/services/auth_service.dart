import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import '../core/config/api_config.dart';

class AuthService extends ChangeNotifier {
  static const _storage = FlutterSecureStorage();
  static const _tokenKey = 'jwt_token';
  static const _userIdKey = 'user_id';
  static const _userEmailKey = 'user_email';
  
  String? _token;
  String? _userId;
  String? _userEmail;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get token => _token;
  String? get userId => _userId;
  String? get userEmail => _userEmail;
  
  AuthService() {
    _loadStoredAuth();
  }
  
  Future<void> _loadStoredAuth() async {
    try {
      _token = await _storage.read(key: _tokenKey);
      _userId = await _storage.read(key: _userIdKey);
      _userEmail = await _storage.read(key: _userEmailKey);
      _isAuthenticated = _token != null;
      if (!_isAuthenticated) {
        await _autoLoginPublicUser();
      } else {
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading stored auth: $e');
    }
  }

  Future<void> _autoLoginPublicUser() async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.loginEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': ApiConfig.testEmail,
          'password': ApiConfig.testPassword,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _token = data['token'];
        _userId = data['user']['id']?.toString();
        _userEmail = data['user']['email'];

        await _storage.write(key: _tokenKey, value: _token);
        await _storage.write(key: _userIdKey, value: _userId);
        await _storage.write(key: _userEmailKey, value: _userEmail);

        _isAuthenticated = true;
        notifyListeners();
      } else {
        debugPrint('Auto-login failed: ${response.statusCode}');
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Auto-login error: $e');
      notifyListeners();
    }
  }
  
  Future<Map<String, dynamic>> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.loginEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _token = data['token'];
        _userId = data['user']['id']?.toString();
        _userEmail = data['user']['email'];
        
        // Store credentials securely
        await _storage.write(key: _tokenKey, value: _token);
        await _storage.write(key: _userIdKey, value: _userId);
        await _storage.write(key: _userEmailKey, value: _userEmail);
        
        _isAuthenticated = true;
        _isLoading = false;
        notifyListeners();
        
        return {'success': true, 'message': 'Login successful'};
      } else {
        _isLoading = false;
        notifyListeners();
        
        final error = jsonDecode(response.body);
        return {
          'success': false,
          'message': error['message'] ?? 'Login failed'
        };
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}'
      };
    }
  }
  
  Future<void> logout() async {
    try {
      if (_token != null) {
        // Call logout endpoint
        await http.post(
          Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.logoutEndpoint}'),
          headers: {
            'Authorization': 'JWT $_token',
          },
        );
      }
    } catch (e) {
      debugPrint('Logout API call failed: $e');
    } finally {
      // Clear local storage regardless
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _userIdKey);
      await _storage.delete(key: _userEmailKey);
      
      _token = null;
      _userId = null;
      _userEmail = null;
      _isAuthenticated = false;
      notifyListeners();
    }
  }
  
  Map<String, String> getAuthHeaders() {
    if (_token == null) {
      throw Exception('No authentication token available');
    }
    return {
      'Authorization': 'JWT $_token',
      'Content-Type': 'application/json',
    };
  }
  
  Future<bool> validateToken() async {
    if (_token == null) return false;
    
    try {
      // Test the token by making a simple API call
      final response = await http.get(
        Uri.parse('${ApiConfig.apiBaseUrl}${ApiConfig.coursesEndpoint}?limit=1'),
        headers: getAuthHeaders(),
      );
      
      if (response.statusCode == 401) {
        // Token expired or invalid
        await logout();
        return false;
      }
      
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Token validation failed: $e');
      return false;
    }
  }
}