import 'package:shared_preferences/shared_preferences.dart';

/// Simple local persistence for per-slide engagement (like/save) used by the
/// experimental reels viewer. This keeps state on-device only.
class UserEngagementService {
  static const String _likedKey = 'engagement_liked_slides';
  static const String _savedKey = 'engagement_saved_slides';

  Future<Set<String>> _getSet(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final list = prefs.getStringList(key) ?? <String>[];
    return list.toSet();
  }

  Future<void> _setSet(String key, Set<String> values) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(key, values.toList());
  }

  Future<Set<String>> getLiked() => _getSet(_likedKey);
  Future<Set<String>> getSaved() => _getSet(_savedKey);

  Future<bool> isLiked(String slideId) async {
    final set = await getLiked();
    return set.contains(slideId);
  }

  Future<bool> isSaved(String slideId) async {
    final set = await getSaved();
    return set.contains(slideId);
  }

  Future<Set<String>> toggleLike(String slideId) async {
    final set = await getLiked();
    if (set.contains(slideId)) {
      set.remove(slideId);
    } else {
      set.add(slideId);
    }
    await _setSet(_likedKey, set);
    return set;
  }

  Future<Set<String>> toggleSave(String slideId) async {
    final set = await getSaved();
    if (set.contains(slideId)) {
      set.remove(slideId);
    } else {
      set.add(slideId);
    }
    await _setSet(_savedKey, set);
    return set;
  }
}


