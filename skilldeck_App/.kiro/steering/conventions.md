# SkillDeck Code Conventions

## Widget Lifecycle Management

### StatefulWidget Best Practices
- Always check `mounted` before calling `setState()` in async operations
- Add `mounted` checks after each `await` in long-running async methods
- Use early returns when widget is no longer mounted

```dart
Future<void> _loadData() async {
  if (!mounted) return;
  setState(() => _isLoading = true);
  
  final result = await someAsyncOperation();
  if (!mounted) return;
  
  setState(() {
    _data = result;
    _isLoading = false;
  });
}
```

## Service Integration Patterns

### API Service Usage
- Use `SupabaseRsService` for Supabase operations (not the old `SupabaseService`)
- Always handle both success and error cases in API responses
- Implement proper timeout handling (20s default)
- Use JWT authentication with automatic token refresh

### Progress Tracking
- Use `ProgressService` for local progress storage
- Combine with API services for comprehensive progress tracking
- Always validate data before updating progress state

## Error Handling
- Use try-catch blocks around all async operations
- Provide user-friendly error messages
- Log errors with `debugPrint()` for development debugging
- Gracefully handle network failures and API errors

## Performance Considerations
- Use `ValueKey` for widgets that need to rebuild on data changes
- Implement proper loading states with minimal UI impact
- Avoid unnecessary API calls by checking data freshness
- Use `mounted` checks to prevent memory leaks