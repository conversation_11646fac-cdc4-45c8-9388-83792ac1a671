# SkillDeck Architecture Patterns

## State Management Patterns

### Provider Pattern Implementation
- Use `ChangeNotifierProvider` for stateful data that needs to notify listeners
- Use `Provider` for services that don't change (ApiService, etc.)
- Wrap the entire app with `MultiProvider` for dependency injection
- Access providers with `Consumer` for reactive UI updates

```dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => ThemeProvider()),
    Provider(create: (_) => ApiService()),
  ],
  child: Consumer<ThemeProvider>(
    builder: (context, themeProvider, _) => MaterialApp(...)
  ),
)
```

## Navigation Patterns

### Page Navigation
- Use `Navigator.push()` for forward navigation
- Pass data through constructor parameters, not global state
- Use proper page transitions for better UX
- Implement back button handling for complex flows

### Bottom Navigation
- Use consistent navigation structure across main tabs
- Maintain separate navigation stacks for each tab
- Handle deep linking appropriately

## Data Flow Patterns

### API Integration Flow
1. Service layer makes API calls
2. Transform API responses to domain models
3. Update local state/storage if needed
4. Notify UI through providers or callbacks
5. Handle errors gracefully with user feedback

### Progress Tracking Flow
1. User completes action (slide, module, etc.)
2. Update local progress via ProgressService
3. Sync with remote API when available
4. Update UI indicators
5. Persist changes locally for offline access

## Widget Composition Patterns

### Reusable Components
- Create small, focused widgets with single responsibilities
- Use composition over inheritance
- Pass callbacks for user interactions
- Make widgets configurable through constructor parameters

### Layout Patterns
- Use `AppPageScaffold` for consistent page structure
- Implement responsive design with proper constraints
- Use `Expanded` and `Flexible` appropriately for dynamic layouts
- Maintain consistent spacing and padding throughout the app