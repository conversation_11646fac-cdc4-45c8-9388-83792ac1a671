# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new beta build to TestFlight"
  lane :beta do
    # Ensure we're on a clean git status
    ensure_git_status_clean
    
    # Authenticate with App Store Connect via API key (non-interactive)
    api_key = app_store_connect_api_key(
      key_id: ENV["ASC_KEY_ID"],
      issuer_id: ENV["ASC_ISSUER_ID"],
      key_filepath: ENV["ASC_KEY_PATH"], # e.g. ~/.private_keys/AuthKey_#{ASC_KEY_ID}.p8
      duration: 1200,
      in_house: false
    )
    
    # Increment build number
    increment_build_number(
      build_number: latest_testflight_build_number(api_key: api_key) + 1,
      xcodeproj: "Runner.xcodeproj"
    )
    
    # Build the app
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      export_method: "app-store",
      output_directory: "./build",
      output_name: "RSPlus.ipa",
      clean: true,
      silent: false,
      export_options: {
        provisioningProfiles: {
          "com.ben25.rs" => "RS Plus App Store"
        }
      }
    )
    
    # Upload to TestFlight
    upload_to_testflight(
      api_key: api_key,
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false,
      notify_external_testers: false
    )
    
    # Commit the version bump
    commit_version_bump(
      message: "Bump build number for TestFlight release",
      xcodeproj: "Runner.xcodeproj"
    )
    
    # Push to git
    push_to_git_remote
  end
  
  desc "Build and upload to TestFlight without incrementing build number"
  lane :upload do
    # Build the app
    build_app(
      workspace: "Runner.xcworkspace", 
      scheme: "Runner",
      export_method: "app-store",
      output_directory: "./build",
      output_name: "RSPlus.ipa",
      clean: true,
      silent: false
    )
    
    # Upload to TestFlight
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false,
      notify_external_testers: false
    )
  end
  
  desc "Setup certificates and provisioning profiles"
  lane :certificates do
    match(
      type: "development",
      app_identifier: "com.ben25.rs",
      readonly: true
    )
    
    match(
      type: "appstore",
      app_identifier: "com.ben25.rs",
      readonly: true
    )
  end
  
  desc "Download metadata from App Store Connect"
  lane :download_metadata do
    deliver(
      download_metadata: true,
      download_screenshots: false,
      force: true
    )
  end
  
  desc "Submit to App Store Review"
  lane :release do
    # Build the app
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner", 
      export_method: "app-store",
      output_directory: "./build",
      output_name: "RSPlus.ipa",
      clean: true
    )
    
    # Upload to App Store Connect and submit for review
    deliver(
      submit_for_review: true,
      automatic_release: false,
      force: true,
      skip_metadata: false,
      skip_screenshots: false,
      precheck_include_in_app_purchases: false
    )
  end
end