# SkillDeck - Microlearning App Architecture

## Overview
SkillDeck is a microlearning app for work zone safety training featuring swipeable talent cards, progress tracking, and interactive content.

## Core Features
1. **Course Management** - Browse courses with modules and completion tracking
2. **Card-based Learning** - Swipeable flashcards with rich content
3. **Progress Tracking** - Local storage of user progress without login
4. **Interactive Content** - Basic interactions like card flipping
5. **Bottom Navigation** - Learn, Browse, Practice tabs

## Technical Architecture

### Data Models
- `Course` - Course information with modules
- `Module` - Module containing learning cards 
- `LearningCard` - Individual card content with text/images
- `UserProgress` - Local progress tracking

### Core Screens
1. **Home/Courses Page** - List of available courses with progress
2. **Course Detail Page** - Module list with completion status
3. **Learning Card Page** - Swipeable card viewer with navigation
4. **Browse/Interactions Page** - Additional learning content
5. **Practice Page** - Review and practice content

### Key Components
- `CourseCard` - Reusable course display widget
- `ModuleCard` - Module display with progress indicators
- `SwipeableCard` - Interactive learning card component
- `ProgressService` - Local storage and progress management

### File Structure
```
lib/
├── main.dart
├── theme.dart
├── models/
│   ├── course.dart
│   ├── module.dart
│   ├── learning_card.dart
│   └── user_progress.dart
├── services/
│   ├── data_service.dart
│   └── progress_service.dart
├── screens/
│   ├── home_page.dart
│   ├── course_detail_page.dart
│   ├── learning_card_page.dart
│   ├── browse_page.dart
│   └── practice_page.dart
└── widgets/
    ├── course_card.dart
    ├── module_card.dart
    └── swipeable_card.dart
```

## Implementation Priority
1. Data models and sample content creation
2. Home page with course list
3. Course detail page with modules
4. Card learning interface with swipe navigation
5. Progress tracking integration
6. Browse and Practice pages
7. Polish UI and add animations