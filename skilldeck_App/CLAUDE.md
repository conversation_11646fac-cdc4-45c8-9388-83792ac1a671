# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Critical Development Rules

### NEVER Run the App
- **DO NOT** execute `flutter run` or any commands that launch the application
- The developer runs the app in a separate terminal
- Focus only on code changes and static analysis

### ALWAYS Run Flutter Analyze
- **MANDATORY**: Run `flutter analyze` before declaring any task complete
- Fix all errors and warnings before considering work done
- Run `flutter analyze` again after fixes to confirm

## Commands

### Development Commands
```bash
# Static analysis (ALWAYS run before completing tasks)
flutter analyze

# Get dependencies
flutter pub get

# Clean build artifacts
flutter clean

# Generate launcher icons
flutter pub run flutter_launcher_icons

# Build for iOS (production)
flutter build ipa --release --build-number <N> --export-options-plist=ios/ExportOptions.plist

# Upload to TestFlight
xcrun altool --upload-app --type ios -f build/ios/ipa/*.ipa --apiKey <KEY_ID> --apiIssuer <ISSUER_ID>

# Fastlane (iOS deployment)
cd ios
bundle exec fastlane beta  # Requires clean git status
```

## Architecture

### Tech Stack
- **Frontend**: Flutter/Dart (v3.6.0+)
- **Backend**: Supabase (fully migrated from Payload CMS)
- **Storage**: Supabase Storage bucket "Media" for images
- **Local Storage**: SharedPreferences for progress tracking
- **State Management**: Provider + local state
- **Navigation**: Material PageRoute
- **Animations**: flutter_animate, card_swiper

### Core Services Architecture

```
SupabaseRsService (lib/services/supabase_rs_service.dart)
├── Handles all Supabase API calls via REST
├── Constructs Storage URLs: https://{projectId}.supabase.co/storage/v1/object/public/Media/media/{filename}
├── Methods: getCourses(), getCourseDetail(), getModuleDetail(), getSlides()
└── Returns ApiResponse<T> with success/error handling

ProgressService (lib/services/progress_service.dart)
├── Manages user progress persistence via SharedPreferences
├── Tracks: course progress, module progress, slide completion
├── Methods: getUserProgress(), markCardComplete(), updateCurrentCardIndex()
└── Singleton pattern for global access
```

### UI Component Hierarchy

```
HomePageClean (main navigation)
├── AppPageScaffold (consistent layout wrapper)
├── CompactProgressIndicators (3 metric circles)
├── Course Cards (with circular progress indicators)
└── Bottom Navigation (Home, Modules, Practice)

CourseDetailPageApi
├── AppSliverHeader
├── ModuleCards (legacy Module mapping)
└── Navigation to ModuleSlidesSwiper

ModuleSlidesSwiper (card-based learning)
├── Full-screen image display (front)
├── Text content (back - tap to flip)
├── Progress tracking integration
└── Swipe navigation with animations
```

### Data Flow
1. Supabase REST API → SupabaseRsService → UI Components
2. User interactions → ProgressService → SharedPreferences
3. Images: Supabase Storage URLs constructed from filenames
4. No direct SQL queries - use REST API endpoints

### Key Model Mappings
- `CourseResponse`: Main course data with `expandedModules`
- `ModuleResponse`: Contains `slideCount` and `slideIds`
- `SlideResponse`: Has `image` (MediaResponse) with `fullUrl`
- Always fetch slides via bulk endpoint for proper image expansion

## Important Constraints

### API & Data Handling
- Images stored in Supabase Storage require URL construction from filename
- Module slide counts must be fetched from expanded course details
- Progress calculation: completedSlides / totalSlides per course
- IDs may be numeric; always coerce to strings

### UI/UX Requirements
- Use `withValues(alpha:)` instead of deprecated `withOpacity()`
- Progress indicators: small circles (50-65px) with thin strokes
- Card flip animations for slide front/back
- Full-screen image display without text overlay
- Minimize UI chrome for maximum content visibility

### Performance Considerations
- Batch API calls when loading multiple courses
- Cache progress data locally
- Use CircularProgressIndicator for loading states
- Implement error boundaries with retry mechanisms

## File Structure Conventions
- Services in `/lib/services/`
- Screens in `/lib/screens/`
- Reusable widgets in `/lib/widgets/`
- Theme configuration in `/lib/core/theme/`
- API models in `/lib/models/`

## Testing & Validation
- Use `flutter analyze` for static analysis
- Check for RenderFlex overflow issues
- Verify image URLs load correctly
- Test progress persistence across app restarts
- Ensure SafeArea usage for status bar compatibility